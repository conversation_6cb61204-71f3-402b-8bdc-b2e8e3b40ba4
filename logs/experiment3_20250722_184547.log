`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_<PERSON>ai from None to 0
开始加载模型...
错误数目列表: [100, 500, 1000, 3000, 5000, 8000]
Bit范围: 16
Type01模式: 3
测试无错误基准...
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 376.34it/s]
 77%|███████▋  | 77/100 [00:00<00:00, 378.83it/s]
100%|██████████| 100/100 [00:00<00:00, 379.28it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:39,  2.49it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:08, 11.78it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:00<00:04, 18.92it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:00<00:03, 24.24it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:00<00:02, 28.14it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:00<00:02, 30.88it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:01<00:02, 32.87it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:01<00:02, 34.31it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:01<00:01, 35.38it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:01<00:01, 35.97it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:01<00:01, 36.25it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:01<00:01, 36.79it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:01<00:01, 37.21it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:01<00:01, 36.91it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:01<00:01, 37.46it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:02<00:01, 37.76it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:02<00:00, 37.85it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:02<00:00, 38.01it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:02<00:00, 37.66it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:02<00:00, 37.86it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:02<00:00, 37.96it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:02<00:00, 38.03it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:02<00:00, 38.29it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:02<00:00, 38.53it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:02<00:00, 38.71it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:03<00:00, 33.12it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
