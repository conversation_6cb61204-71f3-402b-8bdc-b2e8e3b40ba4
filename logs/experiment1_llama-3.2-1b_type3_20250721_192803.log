`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lamba<PERSON>_openai from None to 0
开始加载模型: llama-3.2-1b
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 69

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 363.75it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 360.22it/s]
100%|██████████| 100/100 [00:00<00:00, 361.00it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:52,  1.88it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:30,  3.19it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:24,  4.01it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:20,  4.68it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:18,  5.11it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:17,  5.41it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:16,  5.64it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:16,  5.67it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:15,  5.69it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:15,  5.80it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:15,  5.91it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:14,  6.01it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:14,  6.09it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:14,  6.14it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:02<00:13,  6.19it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:13,  6.23it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:13,  6.22it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:13,  6.24it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:12,  6.26it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:03<00:12,  6.26it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:03<00:12,  6.27it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:03<00:12,  6.26it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:12,  6.20it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:04<00:12,  6.09it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:04<00:12,  6.15it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:04<00:11,  6.21it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:04<00:11,  6.24it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:04<00:11,  6.25it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:05<00:11,  6.29it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:05<00:11,  6.32it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:05<00:10,  6.33it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:05<00:10,  6.34it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:05<00:10,  6.36it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:05<00:10,  6.34it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:06<00:10,  6.34it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:06<00:10,  6.32it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:06<00:09,  6.34it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:06<00:09,  6.34it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:06<00:09,  6.24it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:06<00:09,  6.26it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:06<00:08,  6.73it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:07<00:08,  7.11it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:07<00:07,  7.41it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:07<00:07,  7.61it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:07<00:07,  7.78it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:07<00:06,  7.92it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:07<00:06,  8.02it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:07<00:06,  8.08it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:07<00:06,  8.16it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:08<00:06,  8.20it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:08<00:05,  8.24it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:08<00:05,  8.26it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:08<00:05,  8.24it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:08<00:05,  8.27it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:08<00:05,  8.26it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:08<00:05,  8.27it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:08<00:05,  8.28it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:08<00:05,  8.31it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:09<00:04,  8.22it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:09<00:04,  8.05it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:09<00:04,  8.06it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:09<00:04,  8.12it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:09<00:04,  7.95it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:09<00:04,  8.07it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:09<00:04,  8.12it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:09<00:04,  8.18it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:10<00:04,  8.23it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:10<00:03,  8.24it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:10<00:03,  8.24it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:10<00:03,  8.27it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:10<00:03,  8.28it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:10<00:03,  8.30it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:10<00:03,  8.30it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:10<00:03,  8.29it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:11<00:03,  8.29it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:11<00:02,  8.28it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:11<00:02,  8.27it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:11<00:02,  8.26it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:11<00:02,  8.26it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:11<00:02,  8.05it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:11<00:02,  8.02it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:11<00:02,  8.11it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:12<00:02,  8.15it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:12<00:01,  8.19it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:12<00:01,  8.19it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:12<00:01,  8.22it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:12<00:01,  8.24it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:12<00:01,  8.24it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:12<00:01,  8.25it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:12<00:01,  8.27it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:13<00:01,  8.20it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:13<00:00,  8.13it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:13<00:00,  8.07it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:13<00:00,  8.00it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:13<00:00,  7.90it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:13<00:00,  7.87it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:13<00:00,  7.93it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:13<00:00,  7.99it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:14<00:00,  8.02it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:14<00:00,  7.84it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:14<00:00,  7.06it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 165

  0%|          | 0/100 [00:00<?, ?it/s]
 34%|███▍      | 34/100 [00:00<00:00, 336.72it/s]
 68%|██████▊   | 68/100 [00:00<00:00, 330.08it/s]
100%|██████████| 100/100 [00:00<00:00, 329.84it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:19,  4.96it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:19,  5.01it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:19,  4.91it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:19,  4.88it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:19,  4.89it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:19,  4.92it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:18,  4.95it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:18,  4.99it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:18,  5.00it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:18,  4.85it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:18,  4.81it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:18,  4.64it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:18,  4.76it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:18,  4.74it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:18,  4.67it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:03<00:18,  4.66it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:17,  4.64it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:17,  4.62it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:17,  4.61it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:04<00:17,  4.58it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:04<00:17,  4.60it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:04<00:16,  4.61it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:16,  4.57it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:05<00:16,  4.56it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:05<00:16,  4.62it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:05<00:15,  4.64it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:05<00:15,  4.65it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:05<00:15,  4.67it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:06<00:15,  4.70it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:06<00:14,  4.69it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:06<00:14,  4.69it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:06<00:14,  4.72it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:06<00:14,  4.72it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:07<00:13,  4.73it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:07<00:13,  4.73it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:07<00:13,  4.68it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:07<00:13,  4.59it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:08<00:13,  4.62it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:08<00:12,  4.70it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:08<00:12,  4.74it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:08<00:12,  4.70it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:08<00:12,  4.76it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:09<00:11,  4.82it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:09<00:11,  4.87it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:09<00:11,  4.92it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:09<00:11,  4.86it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:09<00:10,  4.89it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:10<00:11,  4.60it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:10<00:11,  4.52it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:10<00:11,  4.54it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:10<00:10,  4.62it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:11<00:10,  4.71it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:11<00:09,  4.80it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:11<00:09,  4.86it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:11<00:09,  4.87it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:11<00:09,  4.73it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:12<00:08,  4.81it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:12<00:08,  4.85it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:12<00:08,  4.90it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:12<00:08,  4.90it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:12<00:07,  4.90it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:13<00:07,  4.94it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:13<00:07,  4.99it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:13<00:07,  5.02it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:13<00:06,  5.03it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:13<00:06,  5.04it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:14<00:06,  5.06it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:14<00:06,  5.07it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:14<00:06,  5.07it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:14<00:05,  5.06it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:14<00:05,  5.05it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:15<00:05,  5.06it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:15<00:05,  5.07it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:15<00:05,  5.04it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:15<00:04,  5.03it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:15<00:04,  5.04it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:16<00:04,  5.04it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:16<00:04,  5.02it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:16<00:04,  5.03it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:16<00:03,  5.04it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:16<00:03,  5.03it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:17<00:03,  5.04it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:17<00:03,  5.06it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:17<00:03,  5.06it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:17<00:02,  5.06it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:17<00:02,  5.06it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:18<00:02,  5.05it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:18<00:02,  4.97it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:18<00:02,  4.96it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:18<00:02,  4.95it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:18<00:01,  4.93it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:19<00:01,  4.93it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:19<00:01,  4.92it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:19<00:01,  4.91it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:19<00:01,  4.92it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:19<00:00,  4.92it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:20<00:00,  4.92it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:20<00:00,  4.92it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:20<00:00,  4.93it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.88it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.84it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 219

  0%|          | 0/100 [00:00<?, ?it/s]
 31%|███       | 31/100 [00:00<00:00, 308.54it/s]
 66%|██████▌   | 66/100 [00:00<00:00, 331.84it/s]
100%|██████████| 100/100 [00:00<00:00, 332.17it/s]
100%|██████████| 100/100 [00:00<00:00, 329.42it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:28,  3.44it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:27,  3.55it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:26,  3.61it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:26,  3.64it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:25,  3.66it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:25,  3.68it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:25,  3.70it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:24,  3.69it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:25,  3.62it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:24,  3.65it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:24,  3.68it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:23,  3.70it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:23,  3.70it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:23,  3.69it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:23,  3.67it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:22,  3.67it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:22,  3.69it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:04<00:22,  3.72it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:22,  3.66it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:21,  3.70it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:21,  3.71it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:05<00:20,  3.73it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:20,  3.75it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:20,  3.76it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:06<00:19,  3.76it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:19,  3.76it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:19,  3.76it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:19,  3.71it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:07<00:19,  3.72it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:08<00:18,  3.74it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:18,  3.76it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:18,  3.77it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:08<00:17,  3.77it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:09<00:17,  3.77it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.76it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:09<00:17,  3.76it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:09<00:16,  3.74it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:16,  3.70it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:16,  3.63it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:10<00:16,  3.67it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:11<00:15,  3.71it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:15,  3.74it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:11<00:15,  3.75it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:11<00:14,  3.76it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:12<00:14,  3.76it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:12<00:14,  3.77it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:12<00:14,  3.70it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:12<00:13,  3.73it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:13<00:13,  3.76it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:13<00:13,  3.77it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:13<00:12,  3.78it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:13<00:12,  3.79it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:14<00:12,  3.80it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:14<00:12,  3.80it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:14<00:11,  3.81it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:15<00:11,  3.77it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:15<00:11,  3.75it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:15<00:11,  3.77it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:15<00:10,  3.78it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:16<00:10,  3.78it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:16<00:10,  3.79it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:16<00:10,  3.77it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:16<00:09,  3.75it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:17<00:09,  3.74it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:17<00:09,  3.75it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:17<00:09,  3.66it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:17<00:08,  3.68it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:18<00:08,  3.70it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:18<00:08,  3.71it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:18<00:08,  3.71it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:19<00:07,  3.71it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:19<00:07,  3.72it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:19<00:07,  3.74it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:19<00:06,  3.73it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:20<00:06,  3.66it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:20<00:06,  3.67it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:20<00:06,  3.69it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:20<00:05,  3.71it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:21<00:05,  3.73it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:21<00:05,  3.72it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:21<00:05,  3.74it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:22<00:04,  3.73it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:22<00:04,  3.74it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:22<00:04,  3.69it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:22<00:04,  3.66it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:23<00:03,  3.67it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:23<00:03,  3.68it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:23<00:03,  3.69it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:23<00:02,  3.70it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:24<00:02,  3.71it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:24<00:02,  3.72it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:24<00:02,  3.71it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:25<00:01,  3.71it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:25<00:01,  3.62it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:25<00:01,  3.62it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:25<00:01,  3.63it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:26<00:00,  3.65it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:26<00:00,  3.67it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:26<00:00,  3.68it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:26<00:00,  3.68it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:26<00:00,  3.71it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 302

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 366.69it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 372.95it/s]
100%|██████████| 100/100 [00:00<00:00, 373.06it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:35,  2.82it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:34,  2.84it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:34,  2.85it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:33,  2.87it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:33,  2.88it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.83it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:32,  2.85it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:32,  2.87it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:31,  2.88it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:31,  2.90it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:30,  2.89it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:31,  2.84it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:30,  2.85it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:30,  2.84it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:29,  2.87it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:29,  2.88it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:05<00:28,  2.89it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:28,  2.91it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:27,  2.90it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:06<00:27,  2.91it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:27,  2.85it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.86it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:26,  2.88it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:26,  2.89it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:08<00:26,  2.88it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:25,  2.88it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:25,  2.89it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:09<00:25,  2.84it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:25,  2.82it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:24,  2.84it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:10<00:24,  2.85it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:23,  2.86it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:23,  2.87it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:11<00:22,  2.87it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:22,  2.86it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:22,  2.84it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:12<00:22,  2.86it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:21,  2.88it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:13<00:21,  2.88it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:13<00:20,  2.88it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:14<00:20,  2.88it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:14<00:20,  2.88it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:15<00:20,  2.84it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:15<00:19,  2.86it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:15<00:19,  2.86it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:16<00:18,  2.84it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:16<00:20,  2.59it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:17<00:21,  2.39it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:17<00:23,  2.20it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:17<00:21,  2.34it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:18<00:19,  2.49it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:18<00:18,  2.60it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:18<00:17,  2.69it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:19<00:16,  2.76it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:19<00:16,  2.80it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:19<00:15,  2.79it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:20<00:15,  2.79it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:20<00:14,  2.82it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:21<00:14,  2.86it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:21<00:13,  2.88it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:21<00:13,  2.90it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:22<00:13,  2.92it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:22<00:12,  2.94it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:22<00:12,  2.90it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:23<00:11,  2.92it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:23<00:11,  2.94it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:23<00:11,  2.95it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:24<00:10,  2.96it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:24<00:10,  2.96it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:24<00:10,  2.96it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:25<00:09,  2.92it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:25<00:09,  2.92it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:25<00:09,  2.93it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:26<00:08,  2.95it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:26<00:08,  2.96it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:26<00:08,  2.96it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:27<00:07,  2.96it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:27<00:07,  2.91it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:27<00:07,  2.91it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:28<00:06,  2.93it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:28<00:06,  2.95it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:28<00:06,  2.95it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:29<00:05,  2.95it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:29<00:05,  2.95it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:29<00:05,  2.95it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:30<00:04,  2.93it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:30<00:04,  2.93it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:30<00:04,  2.91it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:31<00:03,  2.91it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:31<00:03,  2.89it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:31<00:03,  2.90it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:32<00:02,  2.90it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:32<00:02,  2.87it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:32<00:02,  2.85it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:33<00:01,  2.85it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:33<00:01,  2.86it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:34<00:01,  2.87it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:34<00:00,  2.88it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:34<00:00,  2.89it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:35<00:00,  2.89it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:35<00:00,  2.85it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 353

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 366.14it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 369.59it/s]
100%|██████████| 100/100 [00:00<00:00, 368.93it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:41,  2.40it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:40,  2.42it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:40,  2.42it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:39,  2.42it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:39,  2.43it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:38,  2.44it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:41,  2.24it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:44,  2.07it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:45,  1.98it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:46,  1.93it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:47,  1.89it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:47,  1.87it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:06<00:47,  1.84it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:46,  1.84it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:07<00:47,  1.80it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:08<00:46,  1.79it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:08<00:46,  1.77it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:09<00:46,  1.77it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:09<00:45,  1.77it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:10<00:45,  1.77it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:10<00:44,  1.78it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:11<00:44,  1.77it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:11<00:43,  1.78it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:12<00:42,  1.79it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:13<00:41,  1.79it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:13<00:41,  1.78it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:14<00:40,  1.79it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:14<00:40,  1.79it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:15<00:39,  1.79it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:15<00:39,  1.78it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:16<00:38,  1.78it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:17<00:38,  1.78it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:17<00:37,  1.78it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:18<00:36,  1.79it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:18<00:36,  1.78it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:19<00:35,  1.78it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:19<00:35,  1.78it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:20<00:34,  1.79it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:20<00:34,  1.78it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:21<00:33,  1.79it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:22<00:32,  1.80it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:22<00:32,  1.80it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:23<00:31,  1.81it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:23<00:31,  1.79it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:24<00:30,  1.80it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:24<00:29,  1.80it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:25<00:29,  1.78it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:25<00:29,  1.77it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:26<00:28,  1.78it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:27<00:27,  1.79it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:27<00:27,  1.79it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:28<00:26,  1.79it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:28<00:26,  1.77it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:29<00:25,  1.78it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:29<00:25,  1.76it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:30<00:24,  1.78it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:31<00:24,  1.78it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:31<00:24,  1.75it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:32<00:23,  1.76it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:32<00:22,  1.79it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:33<00:21,  1.85it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:33<00:19,  1.97it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:34<00:17,  2.07it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:34<00:16,  2.16it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:34<00:15,  2.21it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:35<00:16,  2.10it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:35<00:16,  2.01it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:36<00:16,  1.94it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:37<00:16,  1.92it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:37<00:15,  1.90it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:38<00:14,  1.93it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:38<00:13,  2.02it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:39<00:13,  1.95it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:39<00:13,  1.94it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:40<00:12,  2.05it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:40<00:11,  2.13it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:40<00:10,  2.21it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:41<00:09,  2.26it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:41<00:09,  2.31it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:42<00:08,  2.28it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:42<00:08,  2.32it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:42<00:07,  2.35it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:43<00:07,  2.35it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:43<00:06,  2.34it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:44<00:06,  2.35it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:44<00:06,  2.29it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:45<00:05,  2.30it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:45<00:05,  2.31it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:46<00:04,  2.30it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:46<00:04,  2.31it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:46<00:03,  2.31it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:47<00:03,  2.32it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:47<00:03,  2.32it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:48<00:02,  2.30it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:48<00:02,  2.29it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:49<00:01,  2.33it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:49<00:01,  2.35it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:49<00:00,  2.36it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:50<00:00,  2.38it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  2.37it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  1.97it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 443

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 371.34it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 372.26it/s]
100%|██████████| 100/100 [00:00<00:00, 372.60it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:49,  2.01it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:48,  2.03it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:47,  2.02it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:47,  2.02it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:47,  2.02it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:46,  2.03it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:45,  2.03it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:45,  2.02it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:45,  2.02it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:44,  2.02it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:44,  2.02it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:43,  2.02it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:06<00:43,  2.00it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:43,  1.97it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:07<00:43,  1.97it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:07<00:42,  1.98it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:08<00:42,  1.98it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:08<00:41,  1.97it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:09<00:41,  1.96it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:10<00:40,  1.97it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:10<00:39,  1.99it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:11<00:39,  1.99it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:11<00:38,  1.99it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:12<00:38,  1.99it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:12<00:37,  2.00it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:13<00:37,  2.00it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:13<00:36,  2.00it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:14<00:36,  1.99it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:14<00:36,  1.97it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:15<00:35,  1.97it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:15<00:35,  1.97it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:16<00:34,  1.97it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:16<00:34,  1.97it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:17<00:34,  1.92it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:17<00:33,  1.92it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:18<00:33,  1.93it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:18<00:32,  1.93it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:19<00:31,  1.94it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:19<00:31,  1.94it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:20<00:30,  1.97it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:20<00:29,  1.97it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:21<00:29,  1.99it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:21<00:28,  1.99it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:22<00:28,  1.97it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:22<00:27,  1.98it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:23<00:27,  1.99it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:23<00:26,  2.01it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:24<00:25,  2.00it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:24<00:25,  1.98it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:25<00:25,  1.99it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:25<00:24,  1.99it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:26<00:24,  1.99it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:26<00:23,  1.99it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:27<00:23,  1.96it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:27<00:22,  1.96it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:28<00:22,  1.98it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:28<00:21,  1.98it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:29<00:21,  1.99it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:29<00:21,  1.88it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:30<00:22,  1.81it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:30<00:20,  1.87it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:31<00:19,  1.92it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:31<00:19,  1.94it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:32<00:18,  1.95it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:32<00:17,  1.98it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:33<00:17,  2.00it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:33<00:16,  2.01it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:34<00:16,  1.98it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:34<00:15,  1.99it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:35<00:14,  2.00it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:35<00:14,  2.02it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:36<00:13,  2.02it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:36<00:13,  2.02it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:37<00:12,  2.02it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:37<00:12,  2.02it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:38<00:11,  2.02it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:38<00:11,  2.01it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:39<00:11,  2.00it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:39<00:10,  1.99it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:40<00:10,  1.99it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:40<00:09,  1.99it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:41<00:09,  1.99it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:41<00:08,  1.98it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:42<00:08,  1.98it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:42<00:07,  1.98it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:43<00:07,  1.97it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:43<00:06,  1.96it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:44<00:06,  1.96it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:44<00:05,  1.96it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:45<00:05,  1.96it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:45<00:04,  1.97it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:46<00:04,  1.93it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:47<00:03,  1.95it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:47<00:03,  1.95it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:48<00:02,  1.97it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:48<00:02,  1.98it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:49<00:01,  1.88it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:49<00:01,  1.91it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:50<00:00,  1.95it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  1.97it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  1.98it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 14
模型权重位数: torch.float16
Total bits flipped in weights: 493

  0%|          | 0/100 [00:00<?, ?it/s]
 30%|███       | 30/100 [00:00<00:00, 298.13it/s]
 66%|██████▌   | 66/100 [00:00<00:00, 330.21it/s]
100%|██████████| 100/100 [00:00<00:00, 338.80it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:57,  1.73it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:57,  1.70it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:56,  1.72it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:55,  1.73it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:54,  1.74it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:53,  1.75it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<00:53,  1.74it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:52,  1.74it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:52,  1.75it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:05<00:51,  1.75it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:51,  1.74it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:06<00:50,  1.74it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:07<00:49,  1.74it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:08<00:49,  1.75it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:08<00:48,  1.74it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:09<00:48,  1.75it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:09<00:47,  1.75it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:10<00:46,  1.76it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:10<00:45,  1.76it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:11<00:45,  1.75it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:12<00:45,  1.75it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:12<00:44,  1.76it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:13<00:44,  1.75it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:13<00:43,  1.74it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:14<00:42,  1.75it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:14<00:42,  1.76it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:15<00:41,  1.77it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:16<00:40,  1.77it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:16<00:40,  1.75it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:17<00:39,  1.75it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:17<00:39,  1.76it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:18<00:38,  1.76it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:18<00:38,  1.76it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:19<00:37,  1.76it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:19<00:36,  1.77it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:20<00:36,  1.77it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:21<00:35,  1.76it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:21<00:35,  1.75it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:22<00:34,  1.76it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:22<00:34,  1.76it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:23<00:33,  1.76it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:23<00:33,  1.76it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:24<00:32,  1.76it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:25<00:31,  1.78it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:25<00:30,  1.79it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:26<00:30,  1.79it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:26<00:29,  1.79it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:27<00:29,  1.79it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:27<00:28,  1.80it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:28<00:27,  1.81it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:29<00:27,  1.77it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:29<00:27,  1.77it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:30<00:26,  1.75it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:30<00:27,  1.69it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:31<00:26,  1.68it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:31<00:25,  1.70it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:32<00:24,  1.72it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:33<00:24,  1.70it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:33<00:23,  1.72it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:34<00:23,  1.72it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:34<00:22,  1.73it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:35<00:21,  1.74it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:35<00:21,  1.75it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:36<00:21,  1.67it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:37<00:21,  1.66it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:37<00:20,  1.69it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:38<00:19,  1.71it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:38<00:18,  1.72it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:39<00:17,  1.73it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:40<00:17,  1.75it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:40<00:16,  1.76it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:41<00:15,  1.77it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:41<00:15,  1.70it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:42<00:15,  1.71it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:43<00:14,  1.72it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:43<00:14,  1.70it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:44<00:13,  1.70it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:44<00:12,  1.70it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:45<00:12,  1.71it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:45<00:11,  1.69it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:46<00:11,  1.71it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:47<00:10,  1.70it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:47<00:09,  1.72it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:48<00:09,  1.72it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:48<00:08,  1.73it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:49<00:08,  1.67it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:50<00:07,  1.67it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:50<00:07,  1.69it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:51<00:06,  1.70it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:51<00:05,  1.69it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:52<00:05,  1.63it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:53<00:05,  1.52it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:54<00:04,  1.46it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:54<00:04,  1.41it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:55<00:03,  1.40it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:56<00:02,  1.39it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:56<00:02,  1.38it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:57<00:01,  1.38it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:58<00:00,  1.38it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:59<00:00,  1.39it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:59<00:00,  1.69it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5600
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 16
模型权重位数: torch.float16
Total bits flipped in weights: 556

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.19it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 370.17it/s]
100%|██████████| 100/100 [00:00<00:00, 370.30it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:04,  1.55it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:03,  1.55it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<01:02,  1.54it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:02,  1.54it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:01,  1.53it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<01:01,  1.52it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<01:01,  1.51it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<01:00,  1.51it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<01:00,  1.51it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:59,  1.50it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:07<00:59,  1.50it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:58,  1.51it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:08<00:57,  1.51it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:09<00:57,  1.50it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:09<00:56,  1.50it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<00:55,  1.51it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:11<00:54,  1.52it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:11<00:54,  1.51it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:12<00:53,  1.52it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:13<00:52,  1.52it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:13<00:52,  1.52it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:14<00:51,  1.51it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<00:50,  1.51it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:15<00:49,  1.52it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:16<00:49,  1.53it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:17<00:48,  1.52it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:17<00:47,  1.52it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:18<00:47,  1.53it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:19<00:46,  1.53it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:19<00:46,  1.52it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:20<00:45,  1.52it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:21<00:45,  1.51it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:21<00:44,  1.52it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:22<00:43,  1.52it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:23<00:42,  1.52it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:23<00:42,  1.52it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:24<00:41,  1.51it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:25<00:40,  1.53it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:25<00:39,  1.54it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:26<00:38,  1.54it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:26<00:38,  1.54it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:27<00:37,  1.55it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:28<00:36,  1.54it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:28<00:36,  1.53it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:29<00:35,  1.53it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:30<00:35,  1.52it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:30<00:34,  1.53it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:31<00:33,  1.54it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:32<00:33,  1.53it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:32<00:32,  1.54it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:33<00:31,  1.54it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:34<00:30,  1.56it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:34<00:29,  1.57it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:35<00:29,  1.56it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:36<00:28,  1.56it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:36<00:28,  1.57it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:37<00:27,  1.57it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:37<00:26,  1.58it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:38<00:25,  1.59it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:39<00:25,  1.59it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:39<00:24,  1.59it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:40<00:23,  1.60it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:41<00:23,  1.60it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:41<00:22,  1.60it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:42<00:21,  1.60it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:42<00:21,  1.60it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:43<00:20,  1.61it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:44<00:19,  1.61it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:44<00:19,  1.61it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:45<00:18,  1.60it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:45<00:18,  1.61it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:46<00:17,  1.57it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:47<00:17,  1.57it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:47<00:16,  1.58it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:48<00:15,  1.58it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:49<00:15,  1.57it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:49<00:14,  1.56it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:50<00:14,  1.57it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:51<00:13,  1.58it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:51<00:12,  1.57it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:52<00:12,  1.58it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:53<00:11,  1.58it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:53<00:10,  1.59it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:54<00:10,  1.58it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:54<00:09,  1.58it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:55<00:08,  1.57it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:56<00:08,  1.56it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:56<00:07,  1.54it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:57<00:07,  1.54it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:58<00:06,  1.55it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:58<00:05,  1.55it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:59<00:05,  1.54it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [01:00<00:04,  1.54it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [01:00<00:03,  1.55it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [01:01<00:03,  1.49it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [01:02<00:02,  1.49it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [01:02<00:02,  1.50it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [01:03<00:01,  1.51it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [01:04<00:00,  1.52it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:04<00:00,  1.51it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:04<00:00,  1.54it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.0000
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 83

  0%|          | 0/100 [00:00<?, ?it/s]
 36%|███▌      | 36/100 [00:00<00:00, 353.80it/s]
 72%|███████▏  | 72/100 [00:00<00:00, 348.59it/s]
100%|██████████| 100/100 [00:00<00:00, 351.20it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:12,  7.79it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:12,  7.93it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:12,  8.01it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:11,  8.02it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:11,  8.03it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:11,  8.00it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:11,  8.03it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:00<00:11,  8.03it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:11,  8.00it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  7.98it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  7.96it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:11,  7.99it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:10,  7.98it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:01<00:10,  8.01it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:10,  8.03it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:01<00:10,  8.02it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:10,  8.04it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:10,  7.96it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:10,  7.76it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:10,  7.88it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:02<00:09,  7.96it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:02<00:09,  8.00it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:02<00:09,  8.05it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:03<00:09,  8.03it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:09,  8.03it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:09,  8.02it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:09,  7.98it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:09,  7.99it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:03<00:08,  7.99it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:03<00:08,  8.01it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:03<00:08,  8.04it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:08,  8.03it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:08,  8.05it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  8.06it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:08,  8.07it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:07,  8.05it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:04<00:07,  8.03it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:04<00:07,  7.93it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:04<00:07,  7.80it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:07,  7.81it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  7.89it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  7.94it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:07,  7.98it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:07,  7.98it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:05<00:06,  8.00it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:05<00:06,  7.99it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:05<00:06,  7.99it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:06<00:06,  7.98it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  7.96it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  7.98it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:06,  7.97it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:06<00:05,  8.00it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:06<00:05,  8.04it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:06<00:05,  8.06it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:06<00:05,  8.01it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:07<00:05,  7.96it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  7.94it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  7.83it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  7.62it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:07<00:05,  7.62it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:07<00:05,  7.77it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:07<00:04,  7.87it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:07<00:04,  7.94it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:08<00:04,  7.96it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.98it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  8.01it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  8.04it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:08<00:03,  8.04it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:08<00:03,  8.05it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:08<00:03,  8.08it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:08<00:03,  8.10it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:09<00:03,  8.10it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  8.12it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  8.11it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  8.12it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:09<00:02,  8.13it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:09<00:02,  8.14it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:09<00:02,  8.09it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:09<00:02,  8.04it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:10<00:02,  7.87it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  7.83it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.81it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  7.90it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:10<00:02,  7.88it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:10<00:01,  7.94it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:10<00:01,  7.95it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:10<00:01,  7.97it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:11<00:01,  8.01it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  8.03it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  8.04it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  8.04it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:11<00:00,  8.05it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:11<00:00,  8.06it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:11<00:00,  8.07it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:11<00:00,  8.08it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:12<00:00,  8.08it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  8.06it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  8.07it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  8.12it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  8.12it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  7.99it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 167

  0%|          | 0/100 [00:00<?, ?it/s]
 36%|███▌      | 36/100 [00:00<00:00, 359.71it/s]
 72%|███████▏  | 72/100 [00:00<00:00, 359.71it/s]
100%|██████████| 100/100 [00:00<00:00, 361.03it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:21,  4.63it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:20,  4.71it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:20,  4.81it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:20,  4.80it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:19,  4.83it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:19,  4.84it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:19,  4.87it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:19,  4.82it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:19,  4.74it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:18,  4.75it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:18,  4.74it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:18,  4.67it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:18,  4.61it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:19,  4.49it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:18,  4.58it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:03<00:19,  4.41it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:18,  4.57it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:17,  4.69it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:04<00:16,  4.78it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:04<00:16,  4.87it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:04<00:16,  4.87it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:04<00:15,  4.88it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:15,  4.82it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:05<00:15,  4.90it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:05<00:15,  4.88it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:05<00:15,  4.86it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:05<00:14,  4.93it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:05<00:14,  4.98it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:06<00:14,  5.00it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:06<00:13,  5.02it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:06<00:13,  5.03it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:06<00:13,  5.04it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:06<00:13,  5.05it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:07<00:13,  5.05it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:07<00:12,  5.06it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:07<00:12,  5.08it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:07<00:12,  5.07it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:07<00:12,  4.95it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:08<00:12,  4.94it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:08<00:12,  4.98it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:08<00:11,  4.99it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:08<00:11,  5.00it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:08<00:11,  5.03it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:09<00:11,  5.03it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:09<00:10,  5.04it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:09<00:10,  5.06it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:09<00:10,  5.06it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:09<00:10,  5.06it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:10<00:10,  5.06it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:10<00:09,  5.03it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:10<00:09,  4.92it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:10<00:09,  4.94it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:10<00:09,  4.98it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:11<00:09,  5.00it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:11<00:08,  5.01it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:11<00:08,  5.04it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:11<00:08,  5.02it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:11<00:08,  5.02it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:12<00:08,  5.03it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:12<00:07,  5.03it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:12<00:07,  5.01it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:12<00:07,  5.01it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:12<00:07,  4.91it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:13<00:07,  4.91it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:13<00:07,  4.97it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:13<00:06,  5.00it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:13<00:06,  5.04it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:13<00:06,  5.06it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:14<00:06,  5.08it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:14<00:05,  5.09it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:14<00:05,  5.10it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:14<00:05,  5.11it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:14<00:05,  5.12it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:15<00:05,  5.12it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:15<00:04,  5.11it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:15<00:04,  4.97it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:15<00:04,  5.00it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:15<00:04,  5.03it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:16<00:04,  5.05it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:16<00:03,  5.07it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:16<00:03,  5.09it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:16<00:03,  5.08it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:16<00:03,  5.07it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:16<00:03,  5.09it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:17<00:02,  5.08it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:17<00:02,  5.08it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:17<00:02,  5.07it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:17<00:02,  5.05it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:17<00:02,  4.96it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:18<00:02,  4.92it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:18<00:01,  4.96it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:18<00:01,  4.99it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:18<00:01,  4.98it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:18<00:01,  5.00it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:19<00:00,  5.02it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:19<00:00,  5.05it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:19<00:00,  5.03it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:19<00:00,  5.02it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:19<00:00,  4.99it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.98it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.95it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 224

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 366.62it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 354.91it/s]
100%|██████████| 100/100 [00:00<00:00, 359.15it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:27,  3.62it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:26,  3.69it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:26,  3.65it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:26,  3.65it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:25,  3.67it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:25,  3.68it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:24,  3.72it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:24,  3.76it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:24,  3.77it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:23,  3.79it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:23,  3.74it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:23,  3.68it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:24,  3.59it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:24,  3.57it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:23,  3.56it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:23,  3.55it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:23,  3.53it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:04<00:23,  3.52it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:22,  3.53it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:22,  3.57it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:21,  3.60it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:22,  3.53it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:21,  3.58it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:20,  3.62it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:06<00:20,  3.63it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:20,  3.64it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:19,  3.66it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:19,  3.68it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:07<00:19,  3.69it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:08<00:18,  3.69it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:19,  3.60it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:18,  3.63it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:09<00:18,  3.65it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:09<00:18,  3.65it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.66it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:09<00:17,  3.68it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:10<00:17,  3.71it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:16,  3.73it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:16,  3.74it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:10<00:16,  3.67it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:11<00:16,  3.69it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:15,  3.70it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:11<00:15,  3.69it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:12<00:15,  3.70it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:12<00:14,  3.70it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:12<00:14,  3.66it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:12<00:14,  3.67it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:13<00:14,  3.66it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:13<00:14,  3.63it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:13<00:13,  3.62it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:13<00:13,  3.65it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:14<00:13,  3.58it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:14<00:13,  3.58it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:14<00:12,  3.62it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:15<00:12,  3.63it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:15<00:12,  3.65it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:15<00:11,  3.64it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:15<00:11,  3.62it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:16<00:11,  3.59it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:16<00:10,  3.64it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:16<00:10,  3.67it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:16<00:10,  3.69it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:17<00:10,  3.69it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:17<00:09,  3.70it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:17<00:09,  3.68it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:18<00:09,  3.70it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:18<00:08,  3.70it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:18<00:08,  3.66it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:18<00:08,  3.65it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:19<00:08,  3.67it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:19<00:07,  3.68it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:19<00:07,  3.69it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:19<00:07,  3.70it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:20<00:07,  3.70it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:20<00:06,  3.71it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:20<00:06,  3.69it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:21<00:06,  3.68it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:21<00:06,  3.59it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:21<00:05,  3.62it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:21<00:05,  3.65it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:22<00:05,  3.68it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:22<00:04,  3.69it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:22<00:04,  3.69it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:22<00:04,  3.69it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:23<00:04,  3.69it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:23<00:03,  3.68it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:23<00:03,  3.61it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:24<00:03,  3.61it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:24<00:03,  3.63it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:24<00:02,  3.64it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:24<00:02,  3.66it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:25<00:02,  3.67it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:25<00:01,  3.67it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:25<00:01,  3.67it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:25<00:01,  3.68it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:26<00:01,  3.66it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:26<00:00,  3.61it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:26<00:00,  3.62it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:27<00:00,  3.61it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:27<00:00,  3.63it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:27<00:00,  3.65it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 310

  0%|          | 0/100 [00:00<?, ?it/s]
 36%|███▌      | 36/100 [00:00<00:00, 355.75it/s]
 73%|███████▎  | 73/100 [00:00<00:00, 361.55it/s]
100%|██████████| 100/100 [00:00<00:00, 361.08it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:36,  2.70it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:35,  2.75it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:35,  2.77it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:34,  2.79it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:34,  2.75it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.77it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:33,  2.79it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:32,  2.80it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:32,  2.80it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:32,  2.80it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:31,  2.80it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:31,  2.75it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:31,  2.78it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:05<00:30,  2.78it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:31,  2.67it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:32,  2.61it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:06<00:31,  2.68it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:30,  2.72it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:29,  2.72it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:07<00:28,  2.77it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:28,  2.79it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.81it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:27,  2.81it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:26,  2.82it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:09<00:26,  2.83it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:26,  2.77it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:26,  2.79it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:10<00:25,  2.81it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:25,  2.81it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:24,  2.81it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:11<00:24,  2.82it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:24,  2.82it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:24,  2.79it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:12<00:23,  2.80it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:23,  2.81it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:22,  2.83it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:13<00:22,  2.84it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:21,  2.83it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:14<00:21,  2.81it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:14<00:21,  2.76it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:14<00:21,  2.77it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:15<00:21,  2.75it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:15<00:20,  2.79it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:15<00:19,  2.83it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:16<00:19,  2.84it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:16<00:18,  2.85it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:16<00:18,  2.83it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:17<00:18,  2.84it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:17<00:17,  2.86it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:17<00:17,  2.88it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:18<00:16,  2.89it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:18<00:16,  2.91it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:18<00:16,  2.92it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:19<00:15,  2.92it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:19<00:15,  2.87it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:19<00:15,  2.89it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:20<00:15,  2.83it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:20<00:14,  2.83it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:21<00:14,  2.84it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:21<00:14,  2.84it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:21<00:13,  2.84it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:22<00:13,  2.79it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:22<00:13,  2.82it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:22<00:12,  2.84it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:23<00:12,  2.86it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:23<00:11,  2.86it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:23<00:11,  2.87it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:24<00:11,  2.88it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:24<00:10,  2.85it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:24<00:10,  2.86it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:25<00:10,  2.83it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:25<00:11,  2.52it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:26<00:10,  2.57it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:26<00:09,  2.66it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:26<00:09,  2.72it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:27<00:08,  2.73it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:27<00:08,  2.78it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:27<00:07,  2.81it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:28<00:07,  2.82it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:28<00:07,  2.84it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:28<00:06,  2.85it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:29<00:06,  2.87it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:29<00:05,  2.85it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:29<00:05,  2.86it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:30<00:05,  2.86it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:30<00:04,  2.86it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:31<00:04,  2.84it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:31<00:04,  2.85it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:31<00:03,  2.86it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:32<00:03,  2.86it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:32<00:03,  2.86it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:32<00:02,  2.87it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:33<00:02,  2.88it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:33<00:02,  2.88it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:33<00:01,  2.89it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:34<00:01,  2.89it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:34<00:01,  2.88it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:34<00:00,  2.88it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:35<00:00,  2.88it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:35<00:00,  2.88it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:35<00:00,  2.81it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 361

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 370.48it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 370.00it/s]
100%|██████████| 100/100 [00:00<00:00, 368.82it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:54,  1.80it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:54,  1.78it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:53,  1.83it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:49,  1.92it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:46,  2.05it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:44,  2.11it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:42,  2.17it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:41,  2.21it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:40,  2.25it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:39,  2.29it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:38,  2.32it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:37,  2.34it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:36,  2.36it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:36,  2.33it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:36,  2.33it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:07<00:36,  2.33it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:35,  2.35it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:08<00:34,  2.36it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:08<00:34,  2.37it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:08<00:33,  2.38it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:09<00:33,  2.37it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:09<00:32,  2.37it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:10<00:32,  2.40it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:31,  2.41it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:11<00:30,  2.42it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:11<00:30,  2.43it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:11<00:30,  2.41it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:12<00:29,  2.41it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:12<00:29,  2.41it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:13<00:28,  2.43it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:13<00:28,  2.44it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:13<00:27,  2.44it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:14<00:27,  2.43it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:14<00:27,  2.43it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:15<00:26,  2.42it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:15<00:26,  2.43it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:15<00:25,  2.44it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:16<00:25,  2.45it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:16<00:24,  2.45it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:17<00:24,  2.43it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:24,  2.44it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:18<00:23,  2.43it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:18<00:23,  2.44it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:18<00:22,  2.45it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:19<00:22,  2.46it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:19<00:22,  2.43it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:20<00:21,  2.44it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:20<00:21,  2.45it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:20<00:20,  2.45it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:21<00:20,  2.45it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:21<00:19,  2.46it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:22<00:19,  2.42it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:22<00:19,  2.44it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:22<00:18,  2.44it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:23<00:18,  2.45it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:23<00:17,  2.45it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:24<00:17,  2.45it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:24<00:17,  2.42it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:24<00:17,  2.40it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:25<00:16,  2.39it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:25<00:16,  2.41it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:26<00:15,  2.42it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:26<00:15,  2.43it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:27<00:14,  2.41it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:27<00:14,  2.43it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:27<00:15,  2.25it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:28<00:15,  2.11it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:29<00:15,  2.02it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:29<00:15,  1.96it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:30<00:15,  1.93it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:30<00:15,  1.90it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:31<00:14,  1.88it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:31<00:14,  1.87it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:32<00:13,  1.86it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:32<00:13,  1.85it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:33<00:13,  1.83it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:33<00:12,  1.80it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:34<00:12,  1.79it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:35<00:11,  1.77it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:35<00:11,  1.77it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:36<00:10,  1.78it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:36<00:10,  1.76it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:37<00:09,  1.74it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:38<00:09,  1.75it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:38<00:08,  1.76it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:39<00:07,  1.77it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:39<00:07,  1.76it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:40<00:06,  1.77it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:40<00:06,  1.78it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:41<00:05,  1.79it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:41<00:05,  1.77it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:42<00:04,  1.77it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:43<00:03,  1.78it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:43<00:03,  1.79it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:44<00:02,  1.80it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:44<00:02,  1.79it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:45<00:01,  1.81it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:45<00:01,  1.82it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:46<00:00,  1.83it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:46<00:00,  1.84it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:46<00:00,  2.13it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 449

  0%|          | 0/100 [00:00<?, ?it/s]
 35%|███▌      | 35/100 [00:00<00:00, 349.57it/s]
 70%|███████   | 70/100 [00:00<00:00, 349.54it/s]
100%|██████████| 100/100 [00:00<00:00, 347.75it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:53,  1.86it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:52,  1.86it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:56,  1.72it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:59,  1.61it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:00,  1.58it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<01:00,  1.56it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<01:00,  1.55it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<01:00,  1.52it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:59,  1.52it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:55,  1.63it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:51,  1.72it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:49,  1.78it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:07<00:47,  1.83it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:08<00:46,  1.86it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:08<00:45,  1.89it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:09<00:43,  1.92it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:09<00:43,  1.91it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:10<00:42,  1.93it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:10<00:41,  1.96it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:11<00:40,  1.97it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:11<00:40,  1.97it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:12<00:39,  1.95it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:12<00:39,  1.96it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:13<00:39,  1.95it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:13<00:38,  1.95it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:14<00:37,  1.96it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:14<00:37,  1.94it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:15<00:36,  1.95it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:15<00:36,  1.96it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:16<00:35,  1.97it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:16<00:34,  1.97it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:17<00:35,  1.93it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:17<00:34,  1.95it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:18<00:33,  1.96it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:18<00:32,  1.97it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:19<00:32,  1.98it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:19<00:32,  1.96it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:20<00:31,  1.94it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:21<00:31,  1.96it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:21<00:30,  1.97it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:22<00:29,  1.98it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:22<00:29,  1.96it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:23<00:28,  1.97it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:23<00:28,  1.98it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:24<00:27,  1.98it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:24<00:27,  2.00it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:25<00:26,  1.99it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:25<00:26,  1.99it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:26<00:25,  1.99it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:26<00:24,  2.01it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:27<00:24,  2.03it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:27<00:23,  2.03it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:28<00:23,  2.03it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:28<00:22,  2.03it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:28<00:22,  2.03it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:29<00:21,  2.03it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:29<00:21,  2.03it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:30<00:20,  2.03it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:30<00:20,  2.03it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:31<00:19,  2.03it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:31<00:19,  2.03it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:32<00:18,  2.04it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:32<00:18,  2.02it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:33<00:17,  2.03it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:33<00:17,  2.04it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:34<00:16,  2.05it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:34<00:16,  2.01it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:35<00:16,  1.99it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:35<00:15,  1.99it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:36<00:15,  2.00it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:36<00:14,  2.00it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:37<00:13,  2.01it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:37<00:13,  1.99it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:38<00:13,  1.97it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:38<00:12,  1.99it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:39<00:11,  2.01it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:39<00:11,  2.02it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:40<00:11,  1.99it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:40<00:10,  2.01it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:41<00:09,  2.02it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:41<00:09,  2.03it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:42<00:08,  2.03it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:42<00:08,  2.01it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:43<00:07,  2.02it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:43<00:07,  2.03it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:44<00:06,  2.03it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:44<00:06,  2.04it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:45<00:05,  2.01it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:45<00:05,  2.02it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:46<00:04,  2.02it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:46<00:04,  2.03it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:47<00:03,  2.03it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:47<00:03,  2.03it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:48<00:02,  2.03it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:48<00:02,  2.01it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:49<00:01,  2.03it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:49<00:01,  2.04it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:50<00:00,  2.05it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:50<00:00,  2.03it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:51<00:00,  2.03it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:51<00:00,  1.95it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 14
模型权重位数: torch.float16
Total bits flipped in weights: 496

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 375.50it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 375.86it/s]
100%|██████████| 100/100 [00:00<00:00, 375.74it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:13,  1.35it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:12,  1.36it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:02<01:13,  1.32it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:09,  1.39it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:03,  1.51it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:04<00:59,  1.59it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<00:57,  1.63it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<00:55,  1.66it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:53,  1.69it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:52,  1.70it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:51,  1.72it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:53,  1.63it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:08<00:56,  1.53it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:09<00:58,  1.46it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:09<01:01,  1.39it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<01:01,  1.37it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:11<01:01,  1.36it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:12<01:00,  1.35it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:12<01:00,  1.34it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:13<01:00,  1.33it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:14<00:59,  1.33it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:15<00:59,  1.31it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<00:58,  1.32it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:16<00:57,  1.32it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:17<00:57,  1.31it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:18<00:53,  1.38it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:18<00:49,  1.46it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:19<00:47,  1.52it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:19<00:44,  1.58it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:20<00:43,  1.62it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:21<00:41,  1.65it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:21<00:40,  1.67it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:22<00:39,  1.69it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:22<00:38,  1.71it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:23<00:37,  1.72it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:23<00:36,  1.73it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:24<00:36,  1.72it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:25<00:36,  1.72it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:25<00:35,  1.73it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:26<00:34,  1.74it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:26<00:33,  1.74it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:27<00:33,  1.73it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:27<00:32,  1.74it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:28<00:32,  1.74it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:29<00:31,  1.75it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:29<00:31,  1.74it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:30<00:30,  1.75it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:30<00:29,  1.76it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:31<00:29,  1.76it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:31<00:28,  1.74it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:32<00:28,  1.75it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:33<00:27,  1.76it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:33<00:26,  1.78it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:34<00:25,  1.79it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:34<00:25,  1.78it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:35<00:24,  1.79it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:35<00:23,  1.80it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:36<00:23,  1.80it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:37<00:22,  1.79it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:37<00:22,  1.80it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:38<00:21,  1.80it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:38<00:21,  1.79it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:39<00:20,  1.77it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:39<00:20,  1.76it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:40<00:19,  1.76it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:40<00:19,  1.75it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:41<00:18,  1.76it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:42<00:18,  1.73it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:42<00:18,  1.71it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:43<00:17,  1.72it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:43<00:16,  1.73it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:44<00:16,  1.72it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:45<00:15,  1.72it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:45<00:15,  1.72it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:46<00:14,  1.72it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:46<00:13,  1.73it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:47<00:13,  1.71it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:47<00:12,  1.72it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:48<00:12,  1.73it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:49<00:11,  1.74it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:49<00:10,  1.75it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:50<00:10,  1.75it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:50<00:09,  1.75it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:51<00:09,  1.75it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:51<00:08,  1.75it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:52<00:08,  1.73it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:53<00:07,  1.73it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:53<00:06,  1.74it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:54<00:06,  1.74it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:54<00:05,  1.73it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:55<00:05,  1.74it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:55<00:04,  1.74it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:56<00:04,  1.74it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:57<00:03,  1.74it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:57<00:02,  1.73it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:58<00:02,  1.75it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:58<00:01,  1.76it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:59<00:01,  1.77it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:59<00:00,  1.75it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:00<00:00,  1.76it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:00<00:00,  1.65it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5600
llama-3.2-1b - type01=3, error_rate: 5e-10, bit_range: 16
模型权重位数: torch.float16
Total bits flipped in weights: 563

  0%|          | 0/100 [00:00<?, ?it/s]
 34%|███▍      | 34/100 [00:00<00:00, 332.92it/s]
 71%|███████   | 71/100 [00:00<00:00, 354.51it/s]
100%|██████████| 100/100 [00:00<00:00, 356.03it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:04,  1.54it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:03,  1.54it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<01:03,  1.54it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:03,  1.52it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:02,  1.53it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<01:01,  1.54it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<01:00,  1.54it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<00:59,  1.54it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:59,  1.54it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:58,  1.54it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:07<00:58,  1.53it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:57,  1.53it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:08<00:56,  1.53it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:09<00:56,  1.54it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:09<00:55,  1.53it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<00:54,  1.53it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:11<00:53,  1.54it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:11<00:53,  1.53it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:12<00:52,  1.53it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:13<00:52,  1.52it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:13<00:52,  1.52it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:14<00:51,  1.52it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<00:51,  1.50it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:15<00:50,  1.51it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:16<00:49,  1.51it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:17<00:49,  1.50it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:17<00:48,  1.49it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:18<00:48,  1.49it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:19<00:47,  1.50it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:19<00:46,  1.50it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:20<00:46,  1.49it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:21<00:45,  1.49it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:21<00:44,  1.50it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:22<00:43,  1.52it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:23<00:42,  1.52it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:23<00:41,  1.53it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:24<00:40,  1.54it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:24<00:40,  1.53it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:25<00:39,  1.54it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:26<00:38,  1.55it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:26<00:37,  1.56it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:27<00:37,  1.56it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:28<00:36,  1.56it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:28<00:35,  1.56it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:29<00:35,  1.56it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:30<00:34,  1.57it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:30<00:33,  1.56it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:31<00:33,  1.57it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:31<00:32,  1.57it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:32<00:31,  1.57it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:33<00:31,  1.56it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:33<00:30,  1.56it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:34<00:30,  1.55it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:35<00:30,  1.53it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:35<00:29,  1.52it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:36<00:29,  1.52it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:37<00:28,  1.52it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:37<00:27,  1.51it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:38<00:27,  1.52it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:39<00:26,  1.52it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:39<00:25,  1.53it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:40<00:24,  1.52it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:41<00:24,  1.53it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:41<00:23,  1.52it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:42<00:22,  1.54it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:43<00:22,  1.53it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:43<00:21,  1.52it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:44<00:20,  1.53it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:45<00:20,  1.53it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:45<00:19,  1.52it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:46<00:19,  1.52it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:47<00:18,  1.53it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:47<00:17,  1.53it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:48<00:16,  1.54it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:48<00:16,  1.56it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:49<00:15,  1.57it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:50<00:14,  1.57it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:50<00:14,  1.57it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:51<00:13,  1.57it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:52<00:12,  1.57it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:52<00:12,  1.56it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:53<00:11,  1.55it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:54<00:10,  1.55it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:54<00:10,  1.56it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:55<00:09,  1.57it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:56<00:09,  1.55it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:56<00:08,  1.56it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:57<00:07,  1.55it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:57<00:07,  1.55it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:58<00:06,  1.54it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:59<00:05,  1.55it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:59<00:05,  1.55it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [01:00<00:04,  1.55it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [01:01<00:03,  1.55it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [01:01<00:03,  1.55it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [01:02<00:02,  1.55it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [01:03<00:01,  1.56it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [01:03<00:01,  1.55it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [01:04<00:00,  1.55it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:05<00:00,  1.56it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:05<00:00,  1.54it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.0000
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 86

  0%|          | 0/100 [00:00<?, ?it/s]
 36%|███▌      | 36/100 [00:00<00:00, 352.95it/s]
 73%|███████▎  | 73/100 [00:00<00:00, 361.42it/s]
100%|██████████| 100/100 [00:00<00:00, 360.53it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:12,  7.70it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:12,  7.76it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:12,  7.83it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:12,  7.88it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:12,  7.90it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:11,  7.91it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:11,  7.83it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:11,  7.82it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:11,  7.80it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  7.84it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  7.89it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:11,  7.90it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:11,  7.91it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:01<00:10,  7.91it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:10,  7.94it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:10,  7.95it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:10,  7.85it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:10,  7.81it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:10,  7.71it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:10,  7.67it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:02<00:10,  7.62it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:02<00:10,  7.71it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:02<00:09,  7.77it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:03<00:09,  7.82it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:09,  7.84it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:09,  7.73it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:09,  7.63it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:09,  7.72it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:03<00:09,  7.80it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:03<00:08,  7.85it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:03<00:08,  7.91it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:08,  7.93it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:08,  7.92it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  7.95it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:08,  7.98it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:08,  7.97it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:04<00:07,  7.99it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:04<00:07,  7.99it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:04<00:07,  8.00it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:07,  8.02it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  8.01it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  8.01it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:07,  8.02it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:07,  7.99it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:05<00:06,  7.99it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:05<00:06,  7.77it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:05<00:06,  7.74it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:06<00:06,  7.81it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  7.88it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  7.94it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:06,  7.95it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:06<00:06,  7.98it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:06<00:05,  7.99it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:06<00:05,  7.98it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:06<00:05,  7.92it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:07<00:05,  7.94it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  7.96it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  7.98it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  7.99it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:07<00:04,  8.03it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:07<00:04,  8.03it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:07<00:04,  8.06it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:07<00:04,  8.08it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:08<00:04,  7.83it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.69it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  7.18it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  7.09it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:08<00:04,  7.37it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:08<00:04,  7.54it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:08<00:03,  7.66it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:09<00:03,  7.38it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:09<00:03,  7.43it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  7.48it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  7.58it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  7.69it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:09<00:03,  7.72it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:09<00:02,  7.78it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:09<00:02,  7.83it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:10<00:02,  7.87it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:10<00:02,  7.90it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  7.90it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.92it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  7.93it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:10<00:02,  7.94it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:10<00:01,  7.90it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:10<00:01,  7.87it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:11<00:01,  7.82it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:11<00:01,  7.85it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  7.83it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  7.84it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  7.78it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:11<00:01,  7.76it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:11<00:00,  7.77it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:12<00:00,  7.75it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:12<00:00,  7.77it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:12<00:00,  7.78it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  7.75it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  7.76it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  7.77it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  7.81it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  7.82it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 169

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.28it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 348.37it/s]
100%|██████████| 100/100 [00:00<00:00, 335.81it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:22,  4.33it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:22,  4.40it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:21,  4.60it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:20,  4.69it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:19,  4.78it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:19,  4.85it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:19,  4.87it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:19,  4.80it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:18,  4.84it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:18,  4.90it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:18,  4.94it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:17,  4.89it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:18,  4.76it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:17,  4.81it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:17,  4.83it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:03<00:17,  4.85it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:16,  4.89it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:16,  4.93it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:16,  4.91it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:04<00:16,  4.83it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:04<00:16,  4.82it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:04<00:16,  4.86it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:15,  4.89it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:04<00:15,  4.89it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:05<00:15,  4.88it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:05<00:15,  4.90it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:05<00:14,  4.91it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:05<00:14,  4.88it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:05<00:14,  4.90it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:06<00:14,  4.90it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:06<00:15,  4.58it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:06<00:16,  4.23it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:06<00:16,  4.08it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:07<00:16,  3.97it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:07<00:16,  3.91it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:07<00:16,  3.86it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:08<00:16,  3.83it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:08<00:16,  3.81it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:08<00:15,  3.81it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:08<00:15,  3.83it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:09<00:15,  3.79it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:09<00:15,  3.77it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:09<00:15,  3.77it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:09<00:15,  3.69it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:10<00:14,  3.74it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:10<00:14,  3.77it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:10<00:14,  3.78it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:10<00:13,  3.79it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:11<00:13,  3.79it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:11<00:13,  3.80it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:11<00:13,  3.75it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:12<00:12,  3.77it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:12<00:12,  3.79it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:12<00:12,  3.81it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:12<00:11,  3.81it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:13<00:11,  3.79it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:13<00:11,  3.79it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:13<00:11,  3.80it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:13<00:10,  3.83it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:14<00:10,  3.80it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:14<00:10,  3.80it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:14<00:09,  3.84it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:14<00:09,  3.86it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:15<00:09,  3.89it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:15<00:08,  3.91it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:15<00:08,  3.92it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:15<00:08,  3.93it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:16<00:08,  3.94it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:16<00:07,  3.94it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:16<00:07,  3.91it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:16<00:07,  3.88it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:17<00:07,  3.89it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:17<00:06,  3.89it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:17<00:06,  3.90it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:17<00:06,  3.91it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:18<00:06,  3.91it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:18<00:05,  3.91it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:18<00:05,  3.91it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:18<00:05,  3.91it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:19<00:05,  3.83it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:19<00:04,  3.85it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:19<00:04,  3.88it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:20<00:04,  3.90it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:20<00:04,  3.91it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:20<00:03,  3.92it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:20<00:03,  3.92it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:21<00:03,  3.93it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:21<00:03,  3.94it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:21<00:02,  3.92it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:21<00:02,  3.87it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:22<00:02,  3.90it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:22<00:02,  3.90it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:22<00:01,  3.91it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:22<00:01,  3.93it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:23<00:01,  3.94it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:23<00:01,  3.95it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:23<00:00,  3.97it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:23<00:00,  3.98it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:24<00:00,  3.99it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:24<00:00,  3.91it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:24<00:00,  4.11it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 227

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.48it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 369.15it/s]
100%|██████████| 100/100 [00:00<00:00, 368.52it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:36,  2.75it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:35,  2.75it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:31,  3.07it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:29,  3.28it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:27,  3.42it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:26,  3.51it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:26,  3.57it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:25,  3.60it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:25,  3.63it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:24,  3.66it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:24,  3.66it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:24,  3.59it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:23,  3.63it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:23,  3.64it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:23,  3.63it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:23,  3.65it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:22,  3.65it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:05<00:22,  3.65it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:22,  3.65it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:21,  3.65it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:22,  3.58it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:21,  3.60it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:21,  3.61it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:20,  3.62it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:07<00:20,  3.64it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:20,  3.64it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:19,  3.65it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:19,  3.65it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:08<00:19,  3.64it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:08<00:19,  3.61it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:19,  3.58it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:18,  3.61it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:09<00:18,  3.63it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:09<00:18,  3.63it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.64it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:10<00:17,  3.64it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:10<00:17,  3.65it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:16,  3.65it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:16,  3.65it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:11<00:16,  3.60it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:11<00:16,  3.64it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:16,  3.45it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:12<00:16,  3.49it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:12<00:15,  3.53it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:12<00:15,  3.58it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:12<00:14,  3.61it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:13<00:14,  3.61it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:13<00:14,  3.60it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:13<00:14,  3.57it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:13<00:14,  3.57it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:14<00:13,  3.57it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:14<00:13,  3.57it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:14<00:13,  3.57it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:15<00:12,  3.57it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:15<00:12,  3.59it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:15<00:12,  3.59it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:15<00:11,  3.60it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:16<00:11,  3.56it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:16<00:11,  3.57it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:16<00:11,  3.59it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:17<00:10,  3.60it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:17<00:10,  3.60it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:17<00:10,  3.61it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:17<00:09,  3.62it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:18<00:09,  3.62it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:18<00:09,  3.61it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:18<00:09,  3.61it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:18<00:08,  3.58it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:19<00:08,  3.59it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:19<00:08,  3.59it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:19<00:08,  3.60it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:20<00:07,  3.60it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:20<00:07,  3.62it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:20<00:07,  3.62it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:20<00:06,  3.61it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:21<00:06,  3.61it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:21<00:06,  3.58it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:21<00:06,  3.50it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:22<00:05,  3.52it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:22<00:05,  3.55it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:22<00:05,  3.56it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:22<00:05,  3.57it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:23<00:04,  3.57it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:23<00:04,  3.57it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:23<00:04,  3.56it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:24<00:03,  3.52it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:24<00:03,  3.50it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:24<00:03,  3.51it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:24<00:03,  3.54it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:25<00:02,  3.56it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:25<00:02,  3.55it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:25<00:02,  3.57it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:26<00:01,  3.57it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:26<00:01,  3.59it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:26<00:01,  3.42it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:26<00:01,  3.31it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:27<00:00,  3.42it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:27<00:00,  3.50it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:27<00:00,  3.56it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:28<00:00,  3.61it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:28<00:00,  3.57it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 312

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 365.66it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 365.99it/s]
100%|██████████| 100/100 [00:00<00:00, 366.14it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:35,  2.78it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:35,  2.80it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:35,  2.73it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:34,  2.74it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:34,  2.76it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.78it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:33,  2.79it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:33,  2.77it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:32,  2.76it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:32,  2.75it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:32,  2.76it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:32,  2.73it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:31,  2.75it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:05<00:30,  2.79it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:29,  2.84it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:29,  2.86it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:06<00:29,  2.83it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:28,  2.86it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:29,  2.79it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:07<00:28,  2.81it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:28,  2.80it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.79it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:29,  2.62it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:29,  2.60it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:09<00:28,  2.63it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:27,  2.68it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:26,  2.73it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:10<00:26,  2.77it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:25,  2.77it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:25,  2.77it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:11<00:24,  2.77it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:24,  2.80it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:23,  2.82it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:12<00:23,  2.82it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:23,  2.80it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:13<00:22,  2.81it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:13<00:22,  2.83it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:22,  2.79it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:14<00:21,  2.80it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:14<00:21,  2.80it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:14<00:21,  2.79it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:15<00:21,  2.76it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:15<00:20,  2.74it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:15<00:20,  2.68it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:16<00:21,  2.58it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:16<00:21,  2.45it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:17<00:22,  2.37it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:17<00:22,  2.31it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:18<00:22,  2.28it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:18<00:22,  2.24it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:19<00:22,  2.23it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:19<00:21,  2.20it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:20<00:21,  2.19it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:20<00:20,  2.19it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:20<00:20,  2.22it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:21<00:19,  2.31it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:21<00:18,  2.32it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:22<00:17,  2.36it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:22<00:16,  2.47it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:22<00:15,  2.57it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:23<00:14,  2.62it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:23<00:14,  2.65it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:23<00:13,  2.69it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:24<00:13,  2.72it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:24<00:12,  2.75it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:25<00:12,  2.77it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:25<00:11,  2.78it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:25<00:11,  2.80it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:26<00:11,  2.78it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:26<00:10,  2.78it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:26<00:10,  2.77it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:27<00:10,  2.78it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:27<00:09,  2.77it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:27<00:09,  2.76it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:28<00:08,  2.78it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:28<00:08,  2.78it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:28<00:08,  2.79it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:29<00:07,  2.81it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:29<00:07,  2.83it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:30<00:07,  2.83it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:30<00:06,  2.83it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:30<00:06,  2.82it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:31<00:06,  2.82it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:31<00:05,  2.80it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:31<00:05,  2.79it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:32<00:04,  2.80it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:32<00:04,  2.81it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:32<00:04,  2.81it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:33<00:03,  2.81it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:33<00:03,  2.82it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:33<00:03,  2.78it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:34<00:02,  2.82it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:34<00:02,  2.83it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:34<00:02,  2.84it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:35<00:01,  2.83it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:35<00:01,  2.84it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:36<00:01,  2.86it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:36<00:00,  2.82it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:36<00:00,  2.84it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:37<00:00,  2.83it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:37<00:00,  2.70it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 366

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 373.27it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 375.10it/s]
100%|██████████| 100/100 [00:00<00:00, 375.09it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:54,  1.83it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:53,  1.83it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:52,  1.84it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:52,  1.82it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:52,  1.82it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:51,  1.83it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:50,  1.84it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:50,  1.83it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:49,  1.82it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:05<00:49,  1.83it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:48,  1.84it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:06<00:47,  1.83it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:07<00:48,  1.81it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:07<00:47,  1.80it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:08<00:47,  1.80it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:08<00:43,  1.94it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:09<00:40,  2.05it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:09<00:38,  2.13it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:09<00:37,  2.17it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:10<00:35,  2.23it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:10<00:34,  2.26it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:11<00:34,  2.29it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:11<00:33,  2.30it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:12<00:32,  2.31it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:12<00:33,  2.25it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:12<00:32,  2.25it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:13<00:32,  2.23it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:13<00:31,  2.27it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:14<00:30,  2.32it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:14<00:30,  2.27it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:15<00:29,  2.31it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:15<00:29,  2.34it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:15<00:28,  2.35it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:16<00:27,  2.36it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:16<00:27,  2.38it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:17<00:27,  2.36it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:17<00:26,  2.35it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:18<00:26,  2.36it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:18<00:25,  2.37it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:18<00:25,  2.38it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:19<00:24,  2.39it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:19<00:24,  2.38it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:20<00:24,  2.37it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:20<00:23,  2.37it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:21<00:23,  2.39it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:21<00:22,  2.40it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:21<00:21,  2.41it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:22<00:21,  2.40it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:22<00:21,  2.40it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:23<00:20,  2.40it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:23<00:20,  2.40it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:23<00:19,  2.41it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:24<00:19,  2.41it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:24<00:19,  2.40it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:25<00:18,  2.41it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:25<00:18,  2.42it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:26<00:17,  2.43it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:26<00:17,  2.43it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:26<00:16,  2.43it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:27<00:16,  2.40it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:27<00:16,  2.40it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:28<00:15,  2.40it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:28<00:15,  2.40it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:28<00:14,  2.41it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:29<00:14,  2.42it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:29<00:14,  2.40it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:30<00:13,  2.38it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:30<00:13,  2.39it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:31<00:12,  2.40it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:31<00:12,  2.40it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:31<00:12,  2.40it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:32<00:11,  2.39it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:32<00:11,  2.37it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:33<00:10,  2.38it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:33<00:10,  2.40it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:33<00:10,  2.37it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:34<00:09,  2.37it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:34<00:09,  2.37it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:35<00:08,  2.35it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:35<00:08,  2.33it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:36<00:08,  2.33it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:36<00:07,  2.33it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:36<00:07,  2.34it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:37<00:06,  2.34it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:37<00:06,  2.32it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:38<00:05,  2.34it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:38<00:05,  2.34it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:39<00:05,  2.34it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:39<00:04,  2.34it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:39<00:04,  2.34it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:40<00:03,  2.31it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:40<00:03,  2.33it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:41<00:02,  2.35it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:41<00:02,  2.36it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:42<00:02,  2.37it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:42<00:01,  2.37it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:42<00:01,  2.35it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:43<00:00,  2.37it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:43<00:00,  2.37it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:44<00:00,  2.37it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:44<00:00,  2.26it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 456

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 365.60it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 368.32it/s]
100%|██████████| 100/100 [00:00<00:00, 367.92it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:49,  1.99it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:49,  1.98it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:49,  1.97it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:48,  1.97it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:48,  1.98it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:47,  1.98it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:46,  1.98it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:46,  1.97it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:45,  1.98it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:05<00:45,  2.00it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:44,  2.01it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:06<00:43,  2.02it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:06<00:43,  2.01it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:07<00:42,  2.02it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:07<00:42,  2.02it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:07<00:41,  2.02it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:08<00:41,  2.01it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:09<00:40,  2.01it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:09<00:40,  2.01it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:09<00:39,  2.01it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:10<00:39,  2.01it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:11<00:39,  1.99it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:11<00:40,  1.91it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:12<00:39,  1.92it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:12<00:39,  1.91it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:13<00:38,  1.94it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:13<00:37,  1.94it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:14<00:37,  1.94it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:14<00:36,  1.96it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:15<00:35,  1.97it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:15<00:34,  1.99it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:16<00:34,  1.99it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:16<00:33,  1.98it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:17<00:33,  1.98it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:17<00:32,  1.99it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:18<00:32,  1.99it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:18<00:31,  1.99it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:19<00:31,  1.97it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:19<00:31,  1.96it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:20<00:30,  1.97it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:20<00:29,  1.99it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:21<00:29,  1.98it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:21<00:28,  1.98it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:22<00:28,  1.99it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:22<00:27,  1.99it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:23<00:27,  1.99it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:23<00:27,  1.96it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:24<00:26,  1.95it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:24<00:25,  1.96it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:25<00:25,  1.98it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:25<00:24,  1.98it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:26<00:25,  1.91it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:26<00:25,  1.81it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:27<00:24,  1.86it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:27<00:23,  1.92it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:28<00:22,  1.97it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:28<00:21,  2.00it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:29<00:20,  2.02it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:29<00:20,  2.02it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:30<00:19,  2.02it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:30<00:19,  2.02it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:31<00:19,  2.00it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:31<00:18,  1.99it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:32<00:17,  2.01it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:32<00:17,  2.03it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:33<00:16,  2.05it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:33<00:16,  2.04it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:34<00:15,  2.05it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:34<00:15,  2.05it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:35<00:14,  2.01it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:35<00:14,  2.02it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:36<00:13,  2.01it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:36<00:13,  2.01it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:37<00:12,  2.03it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:37<00:12,  2.05it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:38<00:12,  1.99it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:38<00:12,  1.79it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:39<00:12,  1.70it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:40<00:12,  1.65it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:40<00:12,  1.61it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:41<00:12,  1.57it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:42<00:11,  1.55it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:42<00:11,  1.53it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:43<00:10,  1.54it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:44<00:09,  1.52it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:44<00:09,  1.52it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:45<00:08,  1.53it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:46<00:07,  1.53it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:46<00:07,  1.53it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:47<00:06,  1.61it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:47<00:05,  1.71it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:48<00:04,  1.79it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:48<00:03,  1.83it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:49<00:03,  1.88it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:49<00:02,  1.92it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:50<00:02,  1.94it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:50<00:01,  1.96it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:51<00:01,  1.95it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:51<00:00,  1.94it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:52<00:00,  1.95it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:52<00:00,  1.91it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 14
模型权重位数: torch.float16
Total bits flipped in weights: 500

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 364.38it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 368.13it/s]
100%|██████████| 100/100 [00:00<00:00, 368.51it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:14,  1.33it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:14,  1.32it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:02<01:13,  1.32it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:03<01:12,  1.32it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:11,  1.32it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:04<01:12,  1.30it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:05<01:11,  1.31it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:06<01:10,  1.31it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:06<01:08,  1.32it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:07<01:02,  1.43it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:07<00:58,  1.51it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:08<00:55,  1.58it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:09<00:54,  1.61it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:09<00:52,  1.65it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:10<00:50,  1.68it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<00:49,  1.71it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:11<00:48,  1.71it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:12<00:47,  1.72it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:12<00:46,  1.73it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:13<00:46,  1.72it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:13<00:46,  1.72it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:14<00:45,  1.71it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:14<00:44,  1.71it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:15<00:44,  1.72it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:16<00:43,  1.73it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:16<00:43,  1.70it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:17<00:42,  1.71it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:17<00:41,  1.72it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:18<00:41,  1.71it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:19<00:40,  1.71it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:19<00:40,  1.71it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:20<00:39,  1.72it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:20<00:38,  1.73it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:21<00:38,  1.74it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:21<00:37,  1.72it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:22<00:36,  1.73it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:23<00:36,  1.74it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:23<00:35,  1.75it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:24<00:35,  1.74it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:24<00:34,  1.74it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:25<00:33,  1.75it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:25<00:33,  1.75it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:26<00:32,  1.75it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:27<00:32,  1.73it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:27<00:31,  1.73it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:28<00:31,  1.74it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:28<00:30,  1.74it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:29<00:30,  1.68it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:29<00:29,  1.71it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:30<00:28,  1.73it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:31<00:28,  1.75it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:31<00:27,  1.74it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:32<00:26,  1.76it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:32<00:26,  1.77it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:33<00:25,  1.78it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:33<00:24,  1.79it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:34<00:24,  1.75it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:35<00:24,  1.72it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:35<00:24,  1.69it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:36<00:23,  1.69it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:36<00:23,  1.64it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:37<00:22,  1.67it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:38<00:22,  1.68it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:38<00:21,  1.68it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:39<00:20,  1.67it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:39<00:20,  1.67it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:40<00:19,  1.69it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:41<00:18,  1.71it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:41<00:17,  1.73it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:42<00:17,  1.73it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:42<00:16,  1.75it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:43<00:15,  1.76it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:43<00:15,  1.77it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:44<00:14,  1.75it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:45<00:14,  1.75it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:45<00:13,  1.76it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:46<00:13,  1.77it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:46<00:12,  1.75it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:47<00:12,  1.73it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:47<00:11,  1.75it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:48<00:10,  1.76it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:49<00:10,  1.77it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:49<00:09,  1.77it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:50<00:09,  1.76it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:50<00:08,  1.77it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:51<00:07,  1.77it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:51<00:07,  1.76it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:52<00:06,  1.76it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:52<00:06,  1.76it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:53<00:05,  1.76it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:54<00:05,  1.77it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:54<00:04,  1.76it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:55<00:04,  1.74it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:55<00:03,  1.74it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:56<00:02,  1.72it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:57<00:02,  1.70it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:57<00:01,  1.71it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:58<00:01,  1.72it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:58<00:00,  1.71it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:59<00:00,  1.70it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:59<00:00,  1.68it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-09, bit_range: 16
模型权重位数: torch.float16
Total bits flipped in weights: 571

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 363.46it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 365.85it/s]
100%|██████████| 100/100 [00:00<00:00, 366.29it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:25,  1.16it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:25,  1.14it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:02<01:25,  1.14it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:03<01:23,  1.15it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:04<01:22,  1.15it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:05<01:21,  1.15it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:06<01:20,  1.15it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:06<01:20,  1.14it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:07<01:19,  1.14it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:08<01:18,  1.15it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:09<01:16,  1.16it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:10<01:15,  1.16it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:11<01:14,  1.16it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:12<01:13,  1.17it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:12<01:12,  1.17it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:13<01:12,  1.16it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:14<01:10,  1.17it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:15<01:09,  1.17it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:16<01:09,  1.17it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:17<01:08,  1.18it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:18<01:07,  1.17it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:18<01:03,  1.23it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:19<00:59,  1.30it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:20<00:56,  1.34it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:20<00:55,  1.36it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:21<00:52,  1.40it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:22<00:50,  1.43it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:22<00:49,  1.46it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:23<00:48,  1.48it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:24<00:47,  1.48it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:24<00:46,  1.50it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:25<00:45,  1.51it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:26<00:44,  1.52it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:26<00:43,  1.51it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:27<00:42,  1.52it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:28<00:41,  1.53it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:28<00:41,  1.53it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:29<00:40,  1.52it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:30<00:39,  1.54it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:30<00:38,  1.55it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:31<00:37,  1.55it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:31<00:37,  1.55it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:32<00:36,  1.55it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:33<00:35,  1.56it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:33<00:35,  1.55it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:34<00:34,  1.56it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:35<00:33,  1.56it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:35<00:33,  1.57it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:36<00:32,  1.56it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:37<00:32,  1.55it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:37<00:31,  1.56it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:38<00:30,  1.56it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:39<00:30,  1.54it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:39<00:29,  1.53it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:40<00:29,  1.53it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:41<00:28,  1.52it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:41<00:28,  1.51it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:42<00:27,  1.51it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:43<00:27,  1.51it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:43<00:26,  1.52it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:44<00:25,  1.51it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:44<00:24,  1.52it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:45<00:24,  1.53it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:46<00:23,  1.53it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:46<00:23,  1.52it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:47<00:22,  1.52it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:48<00:21,  1.52it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:48<00:21,  1.52it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:49<00:20,  1.51it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:50<00:19,  1.52it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:50<00:18,  1.53it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:51<00:18,  1.53it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:52<00:17,  1.53it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:52<00:16,  1.53it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:53<00:16,  1.54it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:54<00:15,  1.53it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:54<00:14,  1.53it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:55<00:14,  1.53it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:56<00:13,  1.53it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:56<00:13,  1.52it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:57<00:12,  1.52it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:58<00:11,  1.51it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:58<00:11,  1.53it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:59<00:10,  1.53it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [01:00<00:09,  1.53it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [01:00<00:09,  1.54it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [01:01<00:08,  1.55it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [01:01<00:07,  1.54it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [01:02<00:07,  1.55it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [01:03<00:06,  1.56it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [01:03<00:05,  1.56it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [01:04<00:05,  1.55it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [01:05<00:04,  1.56it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [01:05<00:03,  1.55it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [01:06<00:03,  1.52it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [01:07<00:02,  1.51it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [01:07<00:02,  1.48it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [01:08<00:01,  1.50it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [01:09<00:00,  1.52it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:09<00:00,  1.52it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:09<00:00,  1.43it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.0000
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 93

  0%|          | 0/100 [00:00<?, ?it/s]
 33%|███▎      | 33/100 [00:00<00:00, 318.24it/s]
 65%|██████▌   | 65/100 [00:00<00:00, 283.62it/s]
100%|██████████| 100/100 [00:00<00:00, 313.60it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:12,  7.89it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:12,  8.02it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:12,  7.71it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:12,  7.73it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:12,  7.88it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:11,  7.97it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:11,  8.01it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:11,  8.05it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:11,  8.08it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  8.05it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  8.08it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:10,  8.10it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:10,  8.12it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:01<00:10,  8.12it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:10,  8.11it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:01<00:10,  8.13it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:10,  8.15it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:10,  8.14it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:09,  8.15it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:09,  8.17it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:02<00:09,  8.17it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:02<00:09,  8.15it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:02<00:09,  7.94it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:02<00:09,  7.92it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:09,  7.99it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:09,  8.05it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:09,  8.11it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:08,  8.12it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:03<00:08,  8.05it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:03<00:08,  8.00it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:03<00:08,  8.05it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:03<00:08,  8.10it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:08,  8.15it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  8.17it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:07,  8.15it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:08,  7.87it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:04<00:07,  7.90it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:04<00:07,  7.95it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:04<00:07,  7.98it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:04<00:07,  7.99it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  8.01it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  8.01it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:07,  7.84it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:07,  7.72it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:05<00:07,  7.82it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:05<00:06,  7.90it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:05<00:06,  7.95it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:05<00:06,  7.97it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  8.01it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  8.01it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:06,  8.03it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:06<00:05,  8.03it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:06<00:05,  8.03it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:06<00:05,  8.04it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:06<00:05,  8.06it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:06<00:05,  8.06it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  8.07it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  8.08it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  8.04it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:07<00:04,  8.04it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:07<00:04,  8.05it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:07<00:04,  7.96it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:07<00:04,  7.78it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:07<00:04,  7.84it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.92it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  7.95it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  7.98it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:08<00:04,  8.00it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:08<00:03,  8.01it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:08<00:03,  8.02it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:08<00:03,  8.04it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:08<00:03,  8.05it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  8.05it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  8.07it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  8.06it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:09<00:02,  8.02it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:09<00:02,  8.03it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:09<00:02,  8.06it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:09<00:02,  8.08it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:09<00:02,  8.08it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  8.10it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.95it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  7.85it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:10<00:02,  7.91it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:10<00:01,  7.95it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:10<00:01,  7.99it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:10<00:01,  8.01it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:10<00:01,  8.05it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  8.07it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  8.07it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  8.09it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:11<00:00,  8.08it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:11<00:00,  8.07it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:11<00:00,  8.07it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:11<00:00,  8.05it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:11<00:00,  8.07it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  8.11it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  8.08it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  8.08it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  8.08it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  8.02it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 183

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 363.42it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 363.71it/s]
100%|██████████| 100/100 [00:00<00:00, 363.13it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:26,  3.80it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:25,  3.82it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:25,  3.74it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:25,  3.74it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:25,  3.79it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:24,  3.82it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:24,  3.84it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:23,  3.84it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:23,  3.82it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:23,  3.84it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:23,  3.85it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:22,  3.84it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:23,  3.77it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:22,  3.79it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:22,  3.80it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:22,  3.81it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:21,  3.82it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:04<00:21,  3.79it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:04<00:21,  3.78it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:21,  3.81it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:20,  3.80it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:05<00:20,  3.76it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:20,  3.74it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:20,  3.75it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:06<00:20,  3.73it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:06<00:19,  3.72it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:19,  3.71it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:19,  3.73it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:07<00:18,  3.74it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:07<00:18,  3.74it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:18,  3.77it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:18,  3.75it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:08<00:17,  3.77it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:08<00:17,  3.79it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.81it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:09<00:16,  3.80it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:09<00:16,  3.79it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:16,  3.78it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:16,  3.78it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:10<00:15,  3.81it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:10<00:15,  3.81it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:15,  3.76it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:11<00:14,  4.00it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:11<00:13,  4.26it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:11<00:12,  4.44it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:11<00:11,  4.55it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:12<00:11,  4.57it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:12<00:11,  4.66it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:12<00:10,  4.73it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:12<00:10,  4.69it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:12<00:10,  4.71it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:13<00:10,  4.55it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:13<00:10,  4.33it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:13<00:10,  4.19it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:13<00:11,  4.09it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:14<00:10,  4.04it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:14<00:10,  4.01it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:14<00:10,  3.98it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:15<00:10,  3.97it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:15<00:09,  4.07it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:15<00:09,  4.32it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:15<00:08,  4.38it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:15<00:08,  4.39it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:16<00:08,  4.47it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:16<00:07,  4.52it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:16<00:07,  4.51it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:16<00:07,  4.53it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:16<00:07,  4.55it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:17<00:06,  4.56it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:17<00:06,  4.56it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:17<00:06,  4.55it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:17<00:06,  4.57it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:18<00:05,  4.56it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:18<00:05,  4.63it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:18<00:05,  4.59it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:18<00:05,  4.65it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:18<00:04,  4.71it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:19<00:04,  4.71it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:19<00:04,  4.63it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:19<00:04,  4.60it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:19<00:04,  4.41it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:20<00:04,  4.48it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:20<00:03,  4.49it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:20<00:03,  4.51it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:20<00:03,  4.55it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:20<00:03,  4.58it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:21<00:02,  4.51it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:21<00:02,  4.51it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:21<00:02,  4.54it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:21<00:02,  4.54it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:22<00:01,  4.54it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:22<00:01,  4.49it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:22<00:01,  4.50it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:22<00:01,  4.50it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:22<00:01,  4.50it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:23<00:00,  4.50it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:23<00:00,  4.50it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:23<00:00,  4.40it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:23<00:00,  4.43it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:24<00:00,  4.54it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:24<00:00,  4.16it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 262

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.56it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 369.45it/s]
100%|██████████| 100/100 [00:00<00:00, 368.92it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:35,  2.78it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:35,  2.80it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:34,  2.81it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:34,  2.80it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:34,  2.77it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.78it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:30,  3.03it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:28,  3.22it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:27,  3.36it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:26,  3.45it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:25,  3.50it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:24,  3.55it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:25,  3.43it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:26,  3.25it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:25,  3.32it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:25,  3.33it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:05<00:24,  3.36it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:05<00:24,  3.41it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:23,  3.43it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:06<00:23,  3.45it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:06<00:22,  3.47it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:23,  3.36it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:07<00:22,  3.37it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:07<00:23,  3.26it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:07<00:22,  3.32it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:22,  3.32it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:08<00:22,  3.31it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:08<00:21,  3.31it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:08<00:21,  3.33it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:09<00:21,  3.30it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:09<00:20,  3.31it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:09<00:20,  3.32it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:10<00:20,  3.34it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:10<00:19,  3.30it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:10<00:19,  3.30it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:11<00:19,  3.29it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:11<00:19,  3.23it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:11<00:18,  3.28it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:11<00:18,  3.27it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:12<00:18,  3.27it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:12<00:18,  3.28it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:12<00:17,  3.26it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:13<00:17,  3.27it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:13<00:16,  3.32it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:13<00:16,  3.32it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:14<00:16,  3.30it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:14<00:15,  3.34it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:14<00:15,  3.39it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:15<00:16,  3.16it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:15<00:16,  3.08it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:15<00:15,  3.14it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:15<00:15,  3.19it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:16<00:14,  3.23it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:16<00:14,  3.25it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:16<00:13,  3.25it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:17<00:13,  3.24it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:17<00:13,  3.27it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:17<00:12,  3.26it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:18<00:12,  3.29it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:18<00:12,  3.28it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:18<00:11,  3.27it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:19<00:11,  3.28it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:19<00:11,  3.30it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:19<00:11,  3.26it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:19<00:10,  3.27it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:20<00:10,  3.33it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:20<00:09,  3.39it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:20<00:09,  3.35it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:21<00:09,  3.33it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:21<00:09,  3.31it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:21<00:08,  3.31it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:22<00:08,  3.26it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:22<00:08,  3.25it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:22<00:07,  3.26it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:22<00:07,  3.28it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:23<00:07,  3.29it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:23<00:06,  3.30it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:23<00:06,  3.33it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:24<00:06,  3.33it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:24<00:05,  3.36it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:24<00:05,  3.35it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:25<00:05,  3.35it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:25<00:05,  3.40it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:25<00:04,  3.40it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:25<00:04,  3.37it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:26<00:04,  3.36it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:26<00:03,  3.37it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:26<00:03,  3.38it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:27<00:03,  3.36it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:27<00:02,  3.40it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:27<00:02,  3.46it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:27<00:02,  3.50it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:28<00:01,  3.51it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:28<00:01,  3.53it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:28<00:01,  3.55it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:29<00:01,  3.56it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:29<00:00,  3.56it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:29<00:00,  3.52it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:29<00:00,  3.48it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:30<00:00,  3.51it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:30<00:00,  3.31it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 350

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 374.83it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 374.02it/s]
100%|██████████| 100/100 [00:00<00:00, 374.27it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:44,  2.22it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:45,  2.15it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:45,  2.15it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:44,  2.18it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:43,  2.18it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:42,  2.20it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:42,  2.21it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:42,  2.19it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:41,  2.20it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:40,  2.21it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:40,  2.19it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:40,  2.15it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:40,  2.16it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:40,  2.15it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:39,  2.16it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:07<00:38,  2.17it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:37,  2.18it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:08<00:37,  2.19it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:08<00:37,  2.14it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:09<00:36,  2.17it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:09<00:36,  2.19it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:10<00:35,  2.21it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:10<00:34,  2.22it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:34,  2.23it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:11<00:33,  2.22it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:11<00:33,  2.21it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:12<00:33,  2.20it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:12<00:31,  2.27it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:13<00:31,  2.25it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:13<00:32,  2.19it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:14<00:31,  2.18it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:14<00:29,  2.30it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:14<00:27,  2.44it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:15<00:26,  2.51it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:15<00:25,  2.58it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:15<00:24,  2.65it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:16<00:23,  2.73it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:16<00:22,  2.77it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:17<00:21,  2.82it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:17<00:21,  2.84it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:20,  2.86it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:18<00:20,  2.86it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:18<00:20,  2.84it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:18<00:19,  2.82it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:19<00:19,  2.82it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:19<00:19,  2.81it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:19<00:18,  2.82it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:20<00:18,  2.82it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:20<00:18,  2.81it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:20<00:17,  2.80it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:21<00:17,  2.73it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:21<00:17,  2.75it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:21<00:16,  2.78it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:22<00:16,  2.80it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:22<00:16,  2.81it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:23<00:15,  2.81it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:23<00:15,  2.80it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:23<00:15,  2.76it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:24<00:14,  2.79it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:24<00:14,  2.80it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:24<00:13,  2.80it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:25<00:13,  2.83it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:25<00:13,  2.84it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:25<00:12,  2.85it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:26<00:12,  2.81it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:26<00:12,  2.81it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:26<00:11,  2.82it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:27<00:11,  2.84it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:27<00:10,  2.85it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:28<00:10,  2.85it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:28<00:10,  2.82it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:28<00:09,  2.82it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:29<00:09,  2.81it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:29<00:09,  2.81it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:29<00:08,  2.80it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:30<00:08,  2.73it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:30<00:08,  2.74it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:30<00:08,  2.74it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:31<00:07,  2.75it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:31<00:07,  2.76it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:31<00:06,  2.77it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:32<00:06,  2.77it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:32<00:06,  2.78it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:33<00:05,  2.78it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:33<00:05,  2.79it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:33<00:04,  2.80it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:34<00:04,  2.81it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:34<00:04,  2.83it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:34<00:03,  2.84it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:35<00:03,  2.85it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:35<00:03,  2.85it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:35<00:02,  2.85it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:36<00:02,  2.84it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:36<00:02,  2.83it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:36<00:01,  2.83it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:37<00:01,  2.83it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:37<00:01,  2.83it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:38<00:00,  2.81it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:38<00:00,  2.81it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:38<00:00,  2.82it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:38<00:00,  2.58it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 407

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 365.50it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 368.49it/s]
100%|██████████| 100/100 [00:00<00:00, 367.32it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:42,  2.34it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:42,  2.30it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:42,  2.28it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:41,  2.30it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:41,  2.32it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:40,  2.32it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:40,  2.32it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:39,  2.31it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:39,  2.30it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:39,  2.31it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:04<00:38,  2.32it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:37,  2.32it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:37,  2.33it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:37,  2.31it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:36,  2.33it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:06<00:35,  2.34it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:35,  2.35it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:07<00:34,  2.36it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:08<00:34,  2.37it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:08<00:34,  2.34it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:09<00:33,  2.35it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:09<00:32,  2.37it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:09<00:32,  2.37it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:32,  2.34it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:10<00:31,  2.35it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:11<00:31,  2.33it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:11<00:31,  2.34it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:11<00:30,  2.35it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:12<00:30,  2.37it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:12<00:29,  2.37it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:13<00:29,  2.38it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:13<00:28,  2.36it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:14<00:28,  2.38it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:14<00:27,  2.38it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:14<00:27,  2.38it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:15<00:26,  2.38it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:15<00:26,  2.39it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:16<00:26,  2.36it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:16<00:25,  2.37it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:17<00:25,  2.38it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:24,  2.38it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:17<00:24,  2.38it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:18<00:23,  2.39it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:18<00:23,  2.36it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:19<00:23,  2.37it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:19<00:22,  2.38it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:19<00:22,  2.37it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:20<00:21,  2.36it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:20<00:21,  2.34it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:21<00:21,  2.31it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:21<00:21,  2.31it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:22<00:20,  2.31it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:22<00:20,  2.31it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:23<00:19,  2.32it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:23<00:19,  2.31it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:23<00:19,  2.31it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:24<00:18,  2.31it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:24<00:18,  2.32it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:25<00:17,  2.29it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:25<00:17,  2.28it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:26<00:16,  2.29it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:26<00:16,  2.30it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:26<00:15,  2.32it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:27<00:15,  2.32it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:27<00:15,  2.32it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:28<00:14,  2.32it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:28<00:14,  2.32it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:29<00:13,  2.31it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:29<00:13,  2.31it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:29<00:13,  2.31it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:30<00:12,  2.32it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:30<00:12,  2.32it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:31<00:11,  2.32it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:31<00:11,  2.34it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:32<00:10,  2.34it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:32<00:10,  2.33it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:32<00:09,  2.34it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:33<00:09,  2.35it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:33<00:09,  2.33it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:34<00:08,  2.33it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:34<00:08,  2.34it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:35<00:07,  2.35it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:35<00:07,  2.35it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:35<00:06,  2.35it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:36<00:06,  2.32it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:36<00:06,  2.31it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:37<00:05,  2.31it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:37<00:05,  2.31it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:38<00:04,  2.31it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:38<00:04,  2.32it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:38<00:03,  2.30it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:39<00:03,  2.31it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:39<00:03,  2.32it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:40<00:02,  2.32it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:40<00:02,  2.33it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:41<00:01,  2.34it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:41<00:01,  2.32it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:41<00:00,  2.33it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:42<00:00,  2.34it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:42<00:00,  2.34it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:42<00:00,  2.33it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 514

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.22it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 367.36it/s]
100%|██████████| 100/100 [00:00<00:00, 366.75it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:50,  1.98it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:50,  1.93it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:50,  1.92it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:48,  1.96it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:47,  2.00it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:46,  2.01it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:46,  2.02it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:45,  2.03it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:44,  2.03it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:44,  2.04it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:43,  2.05it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:42,  2.06it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:06<00:42,  2.07it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:42,  2.05it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:07<00:41,  2.06it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:07<00:40,  2.05it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:08<00:40,  2.04it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:08<00:39,  2.05it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:09<00:39,  2.04it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:09<00:39,  2.05it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:10<00:38,  2.06it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:10<00:37,  2.06it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:11<00:37,  2.07it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:11<00:36,  2.06it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:12<00:36,  2.07it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:12<00:35,  2.07it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:13<00:35,  2.08it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:13<00:34,  2.08it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:14<00:34,  2.04it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:14<00:34,  2.03it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:15<00:34,  2.02it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:15<00:33,  2.02it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:16<00:33,  2.02it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:16<00:32,  2.00it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:17<00:32,  1.99it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:17<00:31,  2.00it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:18<00:31,  2.01it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:18<00:30,  2.02it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:19<00:30,  2.01it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:19<00:29,  2.02it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:20<00:29,  2.02it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:20<00:28,  2.01it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:21<00:28,  2.02it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:21<00:28,  2.00it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:22<00:27,  2.00it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:22<00:26,  2.01it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:23<00:26,  2.02it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:23<00:25,  2.03it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:24<00:25,  2.02it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:24<00:24,  2.02it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:25<00:24,  2.03it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:25<00:23,  2.03it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:26<00:23,  2.04it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:26<00:22,  2.04it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:27<00:22,  2.02it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:27<00:21,  2.03it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:28<00:21,  2.04it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:28<00:20,  2.04it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:29<00:20,  2.04it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:29<00:19,  2.03it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:30<00:19,  2.03it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:30<00:18,  2.03it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:31<00:18,  2.03it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:31<00:17,  2.04it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:32<00:17,  2.02it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:32<00:16,  2.03it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:33<00:16,  2.03it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:33<00:15,  2.03it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:34<00:15,  2.03it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:34<00:14,  2.01it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:34<00:14,  2.03it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:35<00:13,  2.04it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:35<00:13,  2.04it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:36<00:12,  2.04it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:36<00:12,  2.01it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:37<00:11,  2.02it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:37<00:11,  2.02it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:38<00:10,  2.02it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:38<00:10,  2.02it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:39<00:09,  2.00it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:39<00:09,  2.01it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:40<00:08,  2.02it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:40<00:08,  1.98it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:41<00:08,  1.99it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:41<00:07,  1.98it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:42<00:07,  1.97it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:42<00:06,  1.97it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:43<00:06,  1.96it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:44<00:05,  1.97it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:44<00:05,  1.95it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:45<00:04,  1.96it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:45<00:04,  1.97it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:46<00:03,  1.98it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:46<00:03,  1.98it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:47<00:02,  1.97it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:47<00:02,  1.97it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:48<00:01,  1.96it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:48<00:01,  1.97it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:49<00:00,  1.98it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:49<00:00,  1.96it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:49<00:00,  2.02it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 14
模型权重位数: torch.float16
Total bits flipped in weights: 574

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 363.89it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 367.76it/s]
100%|██████████| 100/100 [00:00<00:00, 368.73it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:57,  1.74it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:57,  1.70it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:55,  1.73it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:56,  1.71it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:55,  1.72it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:54,  1.74it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<00:53,  1.75it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:53,  1.74it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:52,  1.74it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:05<00:51,  1.74it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:50,  1.75it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:06<00:50,  1.73it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:07<00:50,  1.73it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:08<00:49,  1.74it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:08<00:48,  1.75it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:09<00:47,  1.75it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:09<00:47,  1.74it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:10<00:46,  1.75it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:10<00:46,  1.76it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:11<00:45,  1.76it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:12<00:45,  1.74it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:12<00:44,  1.75it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:13<00:43,  1.75it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:13<00:43,  1.76it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:14<00:42,  1.76it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:14<00:42,  1.75it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:15<00:41,  1.75it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:16<00:41,  1.76it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:16<00:40,  1.76it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:17<00:40,  1.74it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:17<00:39,  1.75it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:18<00:38,  1.76it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:18<00:38,  1.76it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:19<00:37,  1.76it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:20<00:36,  1.76it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:20<00:36,  1.77it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:21<00:35,  1.78it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:21<00:34,  1.79it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:22<00:34,  1.79it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:22<00:33,  1.79it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:23<00:32,  1.79it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:23<00:32,  1.79it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:24<00:31,  1.79it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:25<00:31,  1.79it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:25<00:31,  1.77it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:26<00:30,  1.77it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:26<00:30,  1.76it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:27<00:29,  1.75it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:27<00:29,  1.75it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:28<00:28,  1.76it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:29<00:27,  1.78it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:29<00:26,  1.79it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:30<00:26,  1.80it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:30<00:25,  1.80it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:31<00:24,  1.81it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:31<00:24,  1.81it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:32<00:23,  1.81it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:32<00:23,  1.81it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:33<00:22,  1.81it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:34<00:22,  1.79it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:34<00:21,  1.79it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:35<00:21,  1.80it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:35<00:20,  1.80it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:36<00:20,  1.80it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:36<00:19,  1.79it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:37<00:19,  1.79it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:37<00:18,  1.79it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:38<00:17,  1.80it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:39<00:17,  1.79it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:39<00:16,  1.78it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:40<00:16,  1.78it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:40<00:15,  1.78it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:41<00:15,  1.78it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:41<00:14,  1.78it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:42<00:14,  1.78it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:42<00:13,  1.78it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:43<00:12,  1.79it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:44<00:12,  1.80it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:44<00:11,  1.80it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:45<00:11,  1.81it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:45<00:10,  1.80it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:46<00:09,  1.80it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:46<00:09,  1.80it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:47<00:08,  1.79it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:47<00:08,  1.78it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:48<00:07,  1.78it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:49<00:07,  1.78it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:49<00:06,  1.78it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:50<00:06,  1.77it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:50<00:05,  1.76it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:51<00:05,  1.77it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:51<00:04,  1.77it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:52<00:03,  1.78it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:53<00:03,  1.76it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:53<00:02,  1.77it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:54<00:02,  1.77it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:54<00:01,  1.79it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:55<00:01,  1.80it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:55<00:00,  1.78it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:56<00:00,  1.78it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:56<00:00,  1.77it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5500
llama-3.2-1b - type01=3, error_rate: 5e-09, bit_range: 16
模型权重位数: torch.float16
Total bits flipped in weights: 649

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.70it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 372.80it/s]
100%|██████████| 100/100 [00:00<00:00, 372.39it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:03,  1.57it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:02,  1.56it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<01:02,  1.54it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:02,  1.54it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:00,  1.56it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:59,  1.58it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<00:59,  1.57it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<00:58,  1.59it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:57,  1.58it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:56,  1.59it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:56,  1.58it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:55,  1.59it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:08<00:54,  1.60it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:08<00:53,  1.61it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:09<00:52,  1.60it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<00:52,  1.61it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:10<00:51,  1.61it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:11<00:50,  1.61it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:11<00:50,  1.60it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:12<00:49,  1.61it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:13<00:48,  1.61it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:13<00:48,  1.61it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:14<00:47,  1.61it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:15<00:47,  1.61it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:15<00:46,  1.60it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:16<00:46,  1.59it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:16<00:46,  1.58it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:17<00:45,  1.58it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:18<00:44,  1.58it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:18<00:44,  1.57it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:19<00:44,  1.56it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:20<00:43,  1.56it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:20<00:43,  1.55it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:21<00:42,  1.55it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:22<00:41,  1.55it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:22<00:41,  1.55it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:23<00:40,  1.55it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:24<00:40,  1.55it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:24<00:39,  1.55it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:25<00:38,  1.55it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:26<00:38,  1.54it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:26<00:39,  1.48it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:27<00:38,  1.49it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:28<00:37,  1.51it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:28<00:36,  1.52it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:29<00:35,  1.53it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:29<00:34,  1.54it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:30<00:33,  1.53it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:31<00:33,  1.53it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:31<00:32,  1.54it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:32<00:31,  1.54it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:33<00:31,  1.54it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:33<00:30,  1.53it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:34<00:29,  1.54it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:35<00:29,  1.54it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:35<00:28,  1.55it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:36<00:27,  1.54it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:37<00:27,  1.54it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:37<00:26,  1.55it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:38<00:25,  1.56it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:39<00:25,  1.55it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:39<00:24,  1.56it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:40<00:23,  1.56it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:40<00:23,  1.57it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:41<00:22,  1.55it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:42<00:21,  1.55it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:42<00:21,  1.56it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:43<00:20,  1.56it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:44<00:20,  1.55it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:44<00:19,  1.55it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:45<00:18,  1.55it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:46<00:18,  1.55it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:46<00:17,  1.54it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:47<00:16,  1.55it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:48<00:16,  1.56it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:48<00:15,  1.54it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:49<00:15,  1.53it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:50<00:14,  1.55it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:50<00:13,  1.56it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:51<00:12,  1.57it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:51<00:12,  1.57it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:52<00:11,  1.58it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:53<00:10,  1.58it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:53<00:10,  1.57it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:54<00:09,  1.58it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:55<00:08,  1.59it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:55<00:08,  1.59it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:56<00:07,  1.60it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:56<00:06,  1.60it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:57<00:06,  1.60it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:58<00:05,  1.60it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:58<00:04,  1.60it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:59<00:04,  1.60it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [01:00<00:03,  1.60it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [01:00<00:03,  1.58it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [01:01<00:02,  1.57it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [01:01<00:01,  1.56it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [01:02<00:01,  1.56it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [01:03<00:00,  1.56it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:03<00:00,  1.54it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:03<00:00,  1.56it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.0000
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 112

  0%|          | 0/100 [00:00<?, ?it/s]
 34%|███▍      | 34/100 [00:00<00:00, 337.44it/s]
 69%|██████▉   | 69/100 [00:00<00:00, 339.54it/s]
100%|██████████| 100/100 [00:00<00:00, 340.70it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:13,  7.46it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:12,  7.56it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:12,  7.59it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:12,  7.60it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:12,  7.61it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:12,  7.62it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:12,  7.61it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:12,  7.65it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:11,  7.68it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  7.67it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  7.44it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:12,  7.29it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:11,  7.36it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:01<00:11,  7.36it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:02<00:11,  7.29it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:11,  7.29it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:11,  7.30it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:11,  7.34it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:11,  7.35it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:10,  7.39it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:02<00:10,  7.34it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:02<00:10,  7.19it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:03<00:10,  7.16it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:03<00:10,  7.24it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:10,  7.38it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:09,  7.45it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:09,  7.46it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:09,  7.48it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:03<00:09,  7.52it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:04<00:09,  7.16it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:04<00:09,  7.03it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:09,  7.17it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:09,  7.29it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  7.40it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:08,  7.45it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:08,  7.48it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:05<00:08,  7.42it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:05<00:08,  7.44it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:05<00:08,  7.47it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:08,  7.43it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  7.44it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  7.47it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:07,  7.47it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:07,  7.47it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:06<00:07,  7.49it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:06<00:07,  7.56it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:06<00:07,  7.54it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:06<00:06,  7.55it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  7.49it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  7.42it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:06,  7.45it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:07<00:06,  7.49it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:07<00:06,  7.56it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:07<00:06,  7.66it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:07<00:05,  7.74it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:07<00:05,  7.79it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  7.83it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  7.83it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  7.86it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:08<00:05,  7.88it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:08<00:04,  7.88it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:08<00:04,  7.86it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:08<00:04,  7.90it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:08<00:04,  7.92it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.92it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  7.93it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  7.95it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:09<00:04,  7.96it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:09<00:04,  7.75it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:09<00:03,  7.68it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:09<00:03,  7.74it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:09<00:03,  7.80it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  7.84it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  7.87it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  7.89it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:10<00:03,  7.90it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:10<00:02,  7.92it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:10<00:02,  7.92it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:10<00:02,  7.90it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:10<00:02,  7.92it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  7.94it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.94it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  7.97it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:11<00:02,  7.97it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:11<00:01,  7.96it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:11<00:01,  7.95it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:11<00:01,  7.96it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:11<00:01,  7.85it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  7.74it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  7.66it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  7.75it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:12<00:01,  7.82it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:12<00:00,  7.86it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:12<00:00,  7.90it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:13<00:00,  7.94it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:13<00:00,  7.64it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 201

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.65it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 370.21it/s]
100%|██████████| 100/100 [00:00<00:00, 368.82it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:26,  3.76it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:26,  3.76it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:25,  3.74it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:25,  3.77it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:25,  3.78it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:24,  3.80it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:24,  3.78it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:24,  3.80it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:23,  3.81it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:23,  3.81it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:23,  3.81it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:23,  3.82it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:22,  3.80it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:22,  3.79it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:22,  3.78it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:22,  3.76it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:23,  3.53it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:04<00:21,  3.76it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:20,  3.91it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:20,  3.83it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:20,  3.79it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:05<00:20,  3.78it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:20,  3.77it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:20,  3.76it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:06<00:20,  3.74it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:06<00:18,  4.00it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:17,  4.16it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:16,  4.36it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:07<00:15,  4.50it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:07<00:15,  4.61it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:07<00:14,  4.68it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:14,  4.75it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:08<00:13,  4.79it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:08<00:13,  4.83it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:08<00:13,  4.84it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:08<00:13,  4.86it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:09<00:12,  4.87it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:09<00:12,  4.83it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:09<00:12,  4.74it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:09<00:12,  4.73it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:09<00:12,  4.76it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:10<00:12,  4.77it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:10<00:11,  4.79it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:10<00:11,  4.80it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:10<00:11,  4.80it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:10<00:11,  4.82it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:11<00:10,  4.83it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:11<00:10,  4.84it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:11<00:10,  4.85it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:11<00:10,  4.84it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:12<00:10,  4.77it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:12<00:10,  4.73it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:12<00:09,  4.76it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:12<00:09,  4.79it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:12<00:09,  4.81it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:13<00:09,  4.80it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:13<00:08,  4.85it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:13<00:08,  4.88it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:13<00:08,  4.89it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:13<00:08,  4.87it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:14<00:07,  4.89it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:14<00:07,  4.92it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:14<00:07,  4.87it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:14<00:07,  4.83it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:14<00:07,  4.86it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:15<00:06,  4.91it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:15<00:06,  4.93it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:15<00:06,  4.94it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:15<00:06,  4.94it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:15<00:06,  4.97it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:16<00:05,  4.97it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:16<00:05,  4.98it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:16<00:05,  4.97it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:16<00:05,  4.97it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:16<00:05,  4.94it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:17<00:04,  4.86it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:17<00:04,  4.90it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:17<00:04,  4.93it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:17<00:04,  4.93it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:17<00:04,  4.95it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:18<00:03,  4.96it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:18<00:03,  4.97it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:18<00:03,  4.99it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:18<00:03,  4.99it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:18<00:03,  4.98it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:19<00:02,  4.97it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:19<00:02,  4.98it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:19<00:02,  4.89it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:19<00:02,  4.92it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:19<00:02,  4.93it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:20<00:01,  4.92it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:20<00:01,  4.93it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:20<00:01,  4.94it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:20<00:01,  4.95it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:20<00:01,  4.95it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:21<00:00,  4.95it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:21<00:00,  4.94it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:21<00:00,  4.93it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:21<00:00,  4.89it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:22<00:00,  4.79it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:22<00:00,  4.54it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 311

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 367.25it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 367.36it/s]
100%|██████████| 100/100 [00:00<00:00, 368.04it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:28,  3.49it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:29,  3.38it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:28,  3.44it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:27,  3.46it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:27,  3.48it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:26,  3.50it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:26,  3.53it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:25,  3.55it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:25,  3.54it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:25,  3.51it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:25,  3.49it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:25,  3.52it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:24,  3.54it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:24,  3.55it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:23,  3.56it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:23,  3.58it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:23,  3.60it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:05<00:22,  3.59it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:22,  3.60it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:22,  3.52it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:22,  3.53it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:22,  3.54it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:21,  3.56it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:21,  3.58it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:07<00:20,  3.60it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:20,  3.62it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:20,  3.62it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:19,  3.62it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:08<00:19,  3.58it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:08<00:19,  3.58it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:19,  3.59it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:09<00:18,  3.59it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:09<00:18,  3.57it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:09<00:18,  3.59it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.62it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:10<00:17,  3.64it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:10<00:17,  3.62it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:17,  3.57it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:17,  3.54it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:11<00:16,  3.55it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:11<00:16,  3.54it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:16,  3.58it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:12<00:15,  3.57it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:12<00:15,  3.59it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:12<00:15,  3.62it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:12<00:14,  3.64it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:13<00:14,  3.62it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:13<00:14,  3.62it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:13<00:14,  3.64it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:14<00:13,  3.62it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:14<00:13,  3.63it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:14<00:13,  3.62it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:14<00:12,  3.62it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:15<00:12,  3.61it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:15<00:12,  3.59it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:15<00:12,  3.58it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:15<00:12,  3.53it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:16<00:11,  3.54it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:16<00:12,  3.42it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:16<00:11,  3.44it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:17<00:11,  3.43it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:17<00:11,  3.44it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:17<00:10,  3.44it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:18<00:10,  3.45it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:18<00:10,  3.41it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:18<00:09,  3.42it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:18<00:09,  3.44it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:19<00:09,  3.46it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:19<00:08,  3.48it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:19<00:08,  3.45it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:20<00:08,  3.41it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:20<00:08,  3.44it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:20<00:07,  3.47it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:20<00:07,  3.44it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:21<00:07,  3.46it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:21<00:06,  3.48it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:21<00:06,  3.49it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:22<00:06,  3.51it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:22<00:05,  3.52it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:22<00:05,  3.53it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:22<00:05,  3.53it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:23<00:05,  3.54it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:23<00:04,  3.50it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:23<00:04,  3.52it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:24<00:04,  3.53it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:24<00:03,  3.56it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:24<00:03,  3.56it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:24<00:03,  3.56it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:25<00:03,  3.57it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:25<00:02,  3.57it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:25<00:02,  3.59it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:25<00:02,  3.54it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:26<00:01,  3.52it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:26<00:01,  3.51it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:26<00:01,  3.53it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:27<00:01,  3.54it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:27<00:00,  3.57it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:27<00:00,  3.60it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:27<00:00,  3.61it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:28<00:00,  3.61it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:28<00:00,  3.54it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 397

  0%|          | 0/100 [00:00<?, ?it/s]
 28%|██▊       | 28/100 [00:00<00:00, 279.02it/s]
 64%|██████▍   | 64/100 [00:00<00:00, 326.02it/s]
100%|██████████| 100/100 [00:00<00:00, 334.57it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:35,  2.79it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:35,  2.79it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:34,  2.78it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:34,  2.76it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:34,  2.79it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.81it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:33,  2.78it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:33,  2.72it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:33,  2.75it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:32,  2.75it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:32,  2.75it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:31,  2.76it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:31,  2.76it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:05<00:31,  2.76it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:30,  2.74it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:30,  2.74it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:06<00:30,  2.75it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:29,  2.75it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:29,  2.77it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:07<00:28,  2.80it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:28,  2.82it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.83it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:27,  2.85it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:26,  2.86it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:08<00:26,  2.82it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:26,  2.83it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:25,  2.84it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:10<00:25,  2.85it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:24,  2.85it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:24,  2.86it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:11<00:24,  2.86it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:24,  2.82it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:23,  2.83it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:12<00:23,  2.85it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:22,  2.84it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:22,  2.84it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:13<00:22,  2.83it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:22,  2.80it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:13<00:22,  2.68it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:14<00:22,  2.61it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:14<00:22,  2.66it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:15<00:21,  2.70it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:15<00:20,  2.72it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:15<00:20,  2.74it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:16<00:19,  2.76it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:16<00:19,  2.74it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:16<00:19,  2.75it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:17<00:18,  2.78it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:17<00:18,  2.78it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:17<00:17,  2.78it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:18<00:17,  2.79it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:18<00:17,  2.79it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:19<00:16,  2.77it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:19<00:16,  2.76it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:19<00:16,  2.76it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:20<00:15,  2.76it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:20<00:15,  2.77it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:20<00:15,  2.77it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:21<00:14,  2.78it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:21<00:14,  2.78it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:21<00:14,  2.77it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:22<00:13,  2.79it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:22<00:13,  2.78it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:23<00:12,  2.79it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:23<00:12,  2.79it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:23<00:12,  2.79it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:24<00:11,  2.79it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:24<00:11,  2.76it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:24<00:11,  2.77it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:25<00:10,  2.77it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:25<00:10,  2.77it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:25<00:10,  2.76it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:26<00:09,  2.77it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:26<00:09,  2.78it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:27<00:09,  2.76it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:27<00:08,  2.79it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:27<00:08,  2.80it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:28<00:07,  2.82it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:28<00:07,  2.85it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:28<00:06,  2.87it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:29<00:06,  2.89it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:29<00:06,  2.85it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:29<00:06,  2.76it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:30<00:05,  2.76it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:30<00:05,  2.77it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:30<00:05,  2.75it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:31<00:04,  2.76it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:31<00:04,  2.77it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:32<00:03,  2.76it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:32<00:03,  2.76it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:32<00:03,  2.77it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:33<00:02,  2.77it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:33<00:02,  2.77it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:33<00:02,  2.78it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:34<00:01,  2.77it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:34<00:01,  2.77it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:34<00:01,  2.75it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:35<00:00,  2.74it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:35<00:00,  2.71it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:35<00:00,  2.75it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:35<00:00,  2.78it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 473

  0%|          | 0/100 [00:00<?, ?it/s]
 34%|███▍      | 34/100 [00:00<00:00, 332.01it/s]
 71%|███████   | 71/100 [00:00<00:00, 352.95it/s]
100%|██████████| 100/100 [00:00<00:00, 356.07it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:45,  2.16it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:43,  2.25it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:42,  2.28it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:42,  2.28it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:41,  2.30it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:40,  2.29it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:40,  2.27it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:40,  2.29it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:39,  2.30it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:39,  2.30it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:04<00:38,  2.31it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:38,  2.29it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:38,  2.27it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:37,  2.28it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:37,  2.28it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:07<00:36,  2.28it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:36,  2.27it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:07<00:36,  2.24it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:08<00:36,  2.22it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:08<00:35,  2.23it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:09<00:35,  2.26it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:09<00:34,  2.27it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:10<00:33,  2.28it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:33,  2.25it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:11<00:33,  2.27it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:11<00:32,  2.28it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:11<00:31,  2.30it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:12<00:31,  2.30it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:12<00:30,  2.30it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:13<00:30,  2.27it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:13<00:30,  2.29it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:14<00:29,  2.30it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:14<00:29,  2.31it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:14<00:28,  2.31it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:15<00:28,  2.31it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:15<00:28,  2.28it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:16<00:27,  2.30it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:16<00:26,  2.31it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:17<00:26,  2.31it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:17<00:25,  2.32it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:25,  2.32it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:18<00:25,  2.28it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:18<00:24,  2.29it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:19<00:24,  2.31it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:19<00:23,  2.31it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:20<00:23,  2.31it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:20<00:23,  2.29it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:20<00:22,  2.31it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:21<00:22,  2.29it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:21<00:22,  2.22it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:22<00:21,  2.26it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:22<00:20,  2.30it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:23<00:20,  2.32it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:23<00:19,  2.34it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:24<00:19,  2.36it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:24<00:18,  2.36it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:24<00:18,  2.35it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:25<00:17,  2.35it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:25<00:17,  2.37it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:26<00:16,  2.38it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:26<00:16,  2.39it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:26<00:15,  2.39it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:27<00:15,  2.37it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:27<00:15,  2.36it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:28<00:14,  2.36it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:28<00:14,  2.36it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:29<00:13,  2.36it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:29<00:13,  2.36it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:29<00:13,  2.33it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:30<00:12,  2.31it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:30<00:12,  2.32it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:31<00:12,  2.32it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:31<00:11,  2.32it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:32<00:11,  2.32it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:32<00:10,  2.30it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:32<00:10,  2.30it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:33<00:10,  2.30it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:33<00:09,  2.32it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:34<00:08,  2.35it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:34<00:08,  2.36it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:35<00:08,  2.35it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:35<00:07,  2.36it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:35<00:07,  2.36it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:36<00:06,  2.34it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:36<00:06,  2.36it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:37<00:05,  2.37it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:37<00:05,  2.35it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:38<00:05,  2.37it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:38<00:04,  2.38it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:38<00:04,  2.39it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:39<00:03,  2.40it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:39<00:03,  2.40it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:40<00:02,  2.38it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:40<00:02,  2.31it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:41<00:02,  2.33it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:41<00:01,  2.35it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:41<00:01,  2.37it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:42<00:00,  2.38it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:42<00:00,  2.35it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:43<00:00,  2.33it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:43<00:00,  2.32it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 578

  0%|          | 0/100 [00:00<?, ?it/s]
 32%|███▏      | 32/100 [00:00<00:00, 315.08it/s]
 68%|██████▊   | 68/100 [00:00<00:00, 337.87it/s]
100%|██████████| 100/100 [00:00<00:00, 338.02it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:50,  1.96it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:49,  1.98it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:48,  2.00it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:48,  2.00it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:47,  1.99it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:46,  2.01it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:45,  2.03it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:45,  2.04it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:44,  2.04it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:44,  2.00it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:44,  1.99it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:44,  1.98it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:06<00:44,  1.98it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:07<00:43,  1.97it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:07<00:43,  1.96it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:08<00:42,  1.96it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:08<00:42,  1.96it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:09<00:41,  1.97it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:09<00:40,  1.98it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:10<00:40,  1.97it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:10<00:40,  1.96it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:11<00:39,  1.97it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:11<00:39,  1.97it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:12<00:38,  1.97it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:12<00:38,  1.96it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:13<00:37,  1.96it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:13<00:37,  1.97it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:14<00:36,  1.98it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:14<00:35,  1.99it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:15<00:35,  1.97it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:15<00:34,  1.97it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:16<00:34,  1.98it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:16<00:33,  1.99it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:17<00:33,  1.98it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:17<00:32,  1.97it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:18<00:32,  1.96it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:18<00:32,  1.96it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:19<00:31,  1.97it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:19<00:30,  1.98it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:20<00:31,  1.93it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:20<00:30,  1.97it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:21<00:29,  2.00it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:21<00:28,  2.02it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:22<00:27,  2.02it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:22<00:27,  2.03it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:23<00:26,  2.04it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:23<00:25,  2.05it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:24<00:25,  2.06it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:24<00:24,  2.07it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:25<00:24,  2.06it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:25<00:23,  2.06it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:26<00:23,  2.07it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:26<00:22,  2.07it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:27<00:22,  2.08it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:27<00:21,  2.08it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:27<00:21,  2.06it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:28<00:20,  2.07it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:28<00:20,  2.07it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:29<00:20,  2.04it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:29<00:19,  2.01it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:30<00:19,  1.97it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:31<00:19,  1.96it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:31<00:18,  1.95it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:32<00:18,  1.95it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:32<00:17,  1.95it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:33<00:17,  1.92it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:33<00:17,  1.92it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:34<00:16,  1.93it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:34<00:16,  1.92it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:35<00:15,  1.93it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:35<00:14,  1.93it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:36<00:14,  1.95it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:36<00:13,  1.96it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:37<00:13,  1.96it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:37<00:12,  1.94it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:38<00:12,  1.93it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:38<00:11,  1.95it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:39<00:11,  1.98it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:39<00:10,  1.99it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:40<00:10,  2.00it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:40<00:09,  2.00it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:41<00:08,  2.01it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:41<00:08,  2.02it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:42<00:07,  2.02it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:42<00:07,  2.01it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:43<00:06,  2.01it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:43<00:06,  2.01it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:44<00:06,  1.98it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:44<00:05,  1.98it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:45<00:05,  1.97it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:45<00:04,  1.96it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:46<00:04,  1.95it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:46<00:03,  1.96it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:47<00:03,  1.96it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:47<00:02,  1.94it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:48<00:02,  1.95it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:48<00:01,  1.95it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:49<00:01,  1.95it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:49<00:00,  1.95it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  1.94it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  1.98it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 14
模型权重位数: torch.float16
Total bits flipped in weights: 672

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.35it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 372.77it/s]
100%|██████████| 100/100 [00:00<00:00, 373.26it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:13,  1.34it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:13,  1.34it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:02<01:12,  1.33it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:11,  1.34it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:11,  1.34it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:04<01:10,  1.34it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:05<01:09,  1.34it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<01:08,  1.34it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:06<01:07,  1.35it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:07<01:07,  1.34it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:08<01:06,  1.35it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:08<01:05,  1.35it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:09<01:04,  1.34it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:10<01:03,  1.35it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:11<01:02,  1.35it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:11<01:01,  1.37it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:12<00:56,  1.46it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:13<00:52,  1.55it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:13<00:50,  1.61it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:14<00:48,  1.66it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:14<00:46,  1.68it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:15<00:45,  1.72it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<00:44,  1.74it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:16<00:43,  1.75it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:16<00:42,  1.76it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:17<00:41,  1.76it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:18<00:41,  1.77it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:18<00:40,  1.78it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:19<00:39,  1.78it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:19<00:39,  1.77it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:20<00:39,  1.76it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:20<00:38,  1.76it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:21<00:38,  1.75it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:22<00:38,  1.74it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:22<00:37,  1.73it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:23<00:36,  1.74it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:23<00:36,  1.74it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:24<00:35,  1.74it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:24<00:35,  1.72it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:25<00:34,  1.72it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:26<00:34,  1.72it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:26<00:33,  1.73it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:27<00:33,  1.72it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:27<00:32,  1.71it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:28<00:31,  1.72it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:29<00:31,  1.73it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:29<00:30,  1.73it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:30<00:30,  1.73it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:30<00:29,  1.73it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:31<00:28,  1.74it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:31<00:28,  1.75it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:32<00:27,  1.75it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:33<00:27,  1.73it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:33<00:26,  1.74it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:34<00:25,  1.74it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:34<00:25,  1.74it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:35<00:24,  1.73it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:35<00:24,  1.73it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:36<00:23,  1.74it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:37<00:22,  1.74it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:37<00:22,  1.75it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:38<00:22,  1.72it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:38<00:21,  1.72it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:39<00:20,  1.72it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:39<00:20,  1.72it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:40<00:19,  1.71it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:41<00:19,  1.71it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:41<00:18,  1.71it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:42<00:18,  1.72it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:42<00:17,  1.72it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:43<00:16,  1.72it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:44<00:16,  1.73it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:44<00:15,  1.74it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:45<00:14,  1.75it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:45<00:14,  1.73it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:46<00:13,  1.74it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:46<00:13,  1.76it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:47<00:12,  1.77it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:47<00:11,  1.78it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:48<00:11,  1.78it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:49<00:10,  1.79it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:49<00:10,  1.80it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:50<00:09,  1.80it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:50<00:08,  1.80it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:51<00:08,  1.79it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:51<00:07,  1.79it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:52<00:07,  1.78it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:53<00:06,  1.79it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:53<00:06,  1.76it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:54<00:05,  1.75it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:54<00:05,  1.75it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:55<00:04,  1.75it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:55<00:03,  1.76it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:56<00:03,  1.74it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:57<00:02,  1.74it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:57<00:02,  1.75it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:58<00:01,  1.75it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:58<00:01,  1.75it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:59<00:00,  1.75it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:59<00:00,  1.76it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:59<00:00,  1.67it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5600
llama-3.2-1b - type01=3, error_rate: 1e-08, bit_range: 16
模型权重位数: torch.float16
Total bits flipped in weights: 754

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.15it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 360.36it/s]
100%|██████████| 100/100 [00:00<00:00, 355.80it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:04,  1.54it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:03,  1.54it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<01:02,  1.54it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:02,  1.53it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:01,  1.53it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<01:01,  1.54it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<01:00,  1.53it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<01:00,  1.53it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:58,  1.55it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:57,  1.56it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:07<00:56,  1.56it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:56,  1.55it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:08<00:56,  1.55it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:09<00:55,  1.56it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:09<00:54,  1.56it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<00:54,  1.54it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:10<00:53,  1.55it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:11<00:53,  1.54it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:12<00:52,  1.54it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:13<00:53,  1.49it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:13<00:52,  1.49it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:14<00:52,  1.50it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<00:51,  1.50it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:15<00:51,  1.49it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:16<00:50,  1.49it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:17<00:49,  1.49it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:17<00:48,  1.49it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:18<00:48,  1.48it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:19<00:47,  1.49it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:19<00:46,  1.49it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:20<00:46,  1.49it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:21<00:45,  1.49it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:21<00:44,  1.51it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:22<00:43,  1.53it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:22<00:42,  1.54it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:23<00:41,  1.54it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:24<00:40,  1.55it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:24<00:39,  1.55it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:25<00:39,  1.54it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:26<00:38,  1.55it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:26<00:37,  1.56it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:27<00:37,  1.56it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:28<00:36,  1.56it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:28<00:35,  1.56it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:29<00:35,  1.57it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:30<00:34,  1.57it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:30<00:33,  1.57it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:31<00:33,  1.56it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:31<00:32,  1.57it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:32<00:32,  1.56it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:33<00:31,  1.53it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:33<00:31,  1.51it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:34<00:31,  1.52it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:35<00:30,  1.52it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:35<00:29,  1.51it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:36<00:29,  1.51it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:37<00:28,  1.51it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:37<00:27,  1.51it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:38<00:27,  1.50it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:39<00:26,  1.50it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:39<00:25,  1.52it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:40<00:24,  1.52it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:41<00:24,  1.51it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:41<00:24,  1.49it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:42<00:23,  1.50it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:43<00:22,  1.50it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:43<00:21,  1.51it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:44<00:22,  1.45it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:45<00:22,  1.36it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:46<00:23,  1.30it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:46<00:21,  1.37it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:47<00:19,  1.43it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:48<00:18,  1.47it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:48<00:17,  1.49it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:49<00:16,  1.51it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:50<00:15,  1.51it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:50<00:15,  1.52it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:51<00:14,  1.50it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:52<00:13,  1.51it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:52<00:13,  1.51it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:53<00:12,  1.51it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:54<00:11,  1.51it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:54<00:11,  1.51it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:55<00:10,  1.52it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:56<00:09,  1.51it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:56<00:09,  1.51it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:57<00:08,  1.52it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:58<00:07,  1.53it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:58<00:07,  1.53it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:59<00:06,  1.53it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [01:00<00:05,  1.53it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [01:00<00:05,  1.53it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [01:01<00:04,  1.52it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [01:02<00:03,  1.52it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [01:02<00:03,  1.52it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [01:03<00:02,  1.52it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [01:04<00:01,  1.51it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [01:04<00:01,  1.51it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [01:05<00:00,  1.38it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:06<00:00,  1.32it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:06<00:00,  1.51it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.0000
llama-3.2-1b - type01=3, error_rate: 5e-08, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 225

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 366.68it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 367.49it/s]
100%|██████████| 100/100 [00:00<00:00, 367.43it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:12,  7.64it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:12,  7.93it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:12,  8.00it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:11,  8.02it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:12,  7.73it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:12,  7.74it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:11,  7.86it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:11,  7.92it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:11,  7.99it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  8.01it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  8.03it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:10,  8.05it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:10,  8.03it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:01<00:10,  8.06it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:10,  8.08it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:10,  8.09it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:10,  8.09it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:10,  8.09it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:09,  8.12it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:09,  8.12it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:02<00:09,  8.10it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:02<00:09,  8.11it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:02<00:09,  8.09it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:02<00:09,  7.95it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:09,  7.76it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:09,  7.69it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:09,  7.74it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:09,  7.81it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:03<00:08,  7.89it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:03<00:08,  7.98it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:03<00:08,  8.08it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:08,  8.13it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:08,  8.13it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  7.95it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:08,  7.86it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:08,  7.74it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:04<00:08,  7.73it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:04<00:08,  7.70it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:04<00:07,  7.72it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:07,  7.70it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  7.68it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  7.68it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:07,  7.65it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:07,  7.55it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:05<00:07,  7.42it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:05<00:07,  7.40it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:05<00:07,  7.51it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:06<00:06,  7.55it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  7.60it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  7.67it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:06,  7.73it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:06<00:06,  7.77it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:06<00:06,  7.77it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:06<00:05,  7.73it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:07<00:05,  7.75it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:07<00:05,  7.80it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  7.78it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  7.66it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  7.64it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:07<00:05,  7.61it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:07<00:05,  7.59it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:07<00:05,  7.60it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:08<00:04,  7.52it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:08<00:04,  7.36it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.34it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  7.45it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  7.49it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:08<00:04,  7.53it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:08<00:04,  7.41it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:09<00:04,  7.49it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:09<00:03,  7.54it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:09<00:03,  7.50it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  7.55it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  7.57it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  7.57it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:09<00:03,  7.61it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:09<00:03,  7.64it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:10<00:02,  7.68it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:10<00:02,  7.69it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:10<00:02,  7.72it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  7.69it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.64it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  7.43it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:10<00:02,  7.40it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:10<00:02,  7.47it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:11<00:01,  7.51it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:11<00:01,  7.59it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:11<00:01,  7.64it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  7.67it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  7.69it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  7.69it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:11<00:01,  7.69it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:12<00:00,  7.69it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:12<00:00,  7.76it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:12<00:00,  7.73it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:12<00:00,  7.73it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  7.76it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  7.77it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  7.77it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  7.77it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  7.74it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-08, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 412

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 372.15it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 367.37it/s]
100%|██████████| 100/100 [00:00<00:00, 368.36it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:20,  4.92it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:19,  4.98it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:19,  4.98it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:20,  4.72it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:19,  4.80it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:19,  4.81it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:19,  4.85it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:18,  4.87it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:18,  4.86it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:18,  4.88it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:18,  4.91it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:18,  4.69it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:18,  4.66it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:18,  4.71it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:17,  4.73it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:03<00:17,  4.71it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:17,  4.69it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:17,  4.67it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:17,  4.62it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:04<00:17,  4.66it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:04<00:16,  4.66it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:04<00:16,  4.71it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:16,  4.73it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:05<00:15,  4.76it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:05<00:15,  4.78it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:05<00:15,  4.80it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:05<00:15,  4.83it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:05<00:14,  4.84it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:06<00:14,  4.87it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:06<00:14,  4.79it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:06<00:14,  4.85it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:06<00:13,  4.90it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:06<00:13,  4.93it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:07<00:13,  4.94it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:07<00:13,  4.97it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:07<00:12,  4.98it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:07<00:12,  5.00it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:07<00:12,  5.00it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:08<00:12,  5.01it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:08<00:11,  5.01it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:08<00:11,  5.03it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:08<00:11,  4.98it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:08<00:11,  4.92it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:09<00:11,  4.96it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:09<00:11,  4.98it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:09<00:10,  4.99it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:09<00:10,  5.00it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:09<00:10,  5.01it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:10<00:10,  5.02it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:10<00:09,  5.01it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:10<00:09,  5.02it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:10<00:09,  5.02it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:10<00:09,  5.02it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:11<00:09,  5.00it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:11<00:09,  4.89it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:11<00:08,  4.94it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:11<00:08,  4.98it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:11<00:08,  5.00it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:12<00:08,  5.02it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:12<00:07,  5.03it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:12<00:07,  5.04it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:12<00:07,  5.04it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:12<00:07,  5.05it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:13<00:07,  5.05it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:13<00:06,  5.06it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:13<00:06,  4.99it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:13<00:06,  4.78it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:13<00:06,  4.71it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:14<00:06,  4.73it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:14<00:06,  4.73it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:14<00:06,  4.73it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:14<00:05,  4.72it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:14<00:05,  4.73it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:15<00:05,  4.73it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:15<00:05,  4.74it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:15<00:05,  4.75it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:15<00:04,  4.77it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:16<00:04,  4.77it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:16<00:04,  4.45it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:16<00:04,  4.41it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:16<00:04,  4.55it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:16<00:03,  4.61it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:17<00:03,  4.68it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:17<00:03,  4.72it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:17<00:03,  4.76it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:17<00:02,  4.79it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:17<00:02,  4.79it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:18<00:02,  4.82it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:18<00:02,  4.77it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:18<00:02,  4.40it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:18<00:02,  4.08it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:19<00:02,  3.96it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:19<00:01,  3.86it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:19<00:01,  3.83it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:20<00:01,  3.80it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:20<00:01,  3.77it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:20<00:00,  3.78it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:20<00:00,  3.79it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:21<00:00,  3.79it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:21<00:00,  3.73it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:21<00:00,  4.68it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-08, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 590

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 359.42it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 362.09it/s]
100%|██████████| 100/100 [00:00<00:00, 360.72it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:27,  3.61it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:27,  3.54it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:28,  3.46it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:27,  3.51it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:26,  3.57it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:25,  3.62it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:25,  3.66it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:24,  3.68it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:24,  3.69it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:24,  3.71it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:23,  3.72it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:23,  3.70it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:23,  3.67it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:23,  3.70it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:23,  3.54it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:23,  3.60it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:23,  3.61it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:04<00:22,  3.65it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:21,  3.69it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:21,  3.71it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:21,  3.71it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:21,  3.66it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:20,  3.68it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:20,  3.70it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:06<00:20,  3.71it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:19,  3.72it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:19,  3.73it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:19,  3.73it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:07<00:18,  3.74it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:08<00:18,  3.74it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:18,  3.67it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:18,  3.70it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:08<00:18,  3.71it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:09<00:17,  3.72it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.73it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:09<00:17,  3.74it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:10<00:16,  3.75it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:16,  3.76it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:16,  3.75it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:10<00:16,  3.68it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:11<00:15,  3.69it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:15,  3.65it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:11<00:15,  3.63it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:11<00:15,  3.63it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:12<00:15,  3.60it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:12<00:15,  3.50it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:12<00:14,  3.55it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:13<00:14,  3.59it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:13<00:14,  3.60it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:13<00:13,  3.62it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:13<00:13,  3.64it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:14<00:13,  3.49it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:14<00:13,  3.41it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:14<00:13,  3.49it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:15<00:12,  3.54it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:15<00:12,  3.59it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:15<00:11,  3.62it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:15<00:11,  3.62it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:16<00:11,  3.64it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:16<00:10,  3.66it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:16<00:10,  3.67it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:16<00:10,  3.66it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:17<00:10,  3.67it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:17<00:09,  3.68it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:17<00:09,  3.69it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:18<00:09,  3.69it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:18<00:08,  3.68it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:18<00:08,  3.67it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:18<00:08,  3.68it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:19<00:08,  3.69it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:19<00:07,  3.68it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:19<00:07,  3.68it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:19<00:07,  3.68it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:20<00:07,  3.67it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:20<00:06,  3.67it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:20<00:06,  3.67it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:21<00:06,  3.65it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:21<00:06,  3.64it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:21<00:05,  3.59it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:21<00:05,  3.53it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:22<00:05,  3.56it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:22<00:05,  3.58it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:22<00:04,  3.60it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:23<00:04,  3.60it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:23<00:04,  3.61it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:23<00:03,  3.55it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:23<00:03,  3.59it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:24<00:03,  3.61it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:24<00:03,  3.63it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:24<00:02,  3.64it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:24<00:02,  3.64it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:25<00:02,  3.65it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:25<00:01,  3.65it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:25<00:01,  3.66it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:26<00:01,  3.59it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:26<00:01,  3.61it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:26<00:00,  3.62it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:26<00:00,  3.64it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:27<00:00,  3.64it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:27<00:00,  3.64it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:27<00:00,  3.64it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-08, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 821

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.70it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 370.36it/s]
100%|██████████| 100/100 [00:00<00:00, 370.74it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:35,  2.82it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:36,  2.72it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:34,  2.78it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:34,  2.81it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:33,  2.82it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.81it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:33,  2.82it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:32,  2.80it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:32,  2.77it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:32,  2.77it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:31,  2.79it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:31,  2.79it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:30,  2.81it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:05<00:30,  2.81it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:30,  2.81it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:30,  2.77it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:06<00:29,  2.78it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:29,  2.78it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:29,  2.77it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:07<00:28,  2.77it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:28,  2.74it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.79it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:27,  2.79it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:27,  2.81it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:08<00:26,  2.82it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:26,  2.83it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:25,  2.83it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:10<00:25,  2.82it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:25,  2.76it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:25,  2.77it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:11<00:25,  2.71it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:24,  2.76it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:24,  2.77it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:12<00:23,  2.81it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:22,  2.84it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:22,  2.87it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:13<00:21,  2.87it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:21,  2.85it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:14<00:23,  2.61it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:14<00:25,  2.34it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:15<00:26,  2.19it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:15<00:27,  2.12it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:16<00:27,  2.07it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:16<00:27,  2.06it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:16<00:24,  2.25it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:17<00:22,  2.42it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:17<00:20,  2.56it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:17<00:19,  2.67it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:18<00:18,  2.74it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:18<00:18,  2.76it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:19<00:17,  2.79it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:19<00:16,  2.84it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:19<00:16,  2.88it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:20<00:15,  2.91it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:20<00:15,  2.93it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:20<00:14,  2.95it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:21<00:14,  2.92it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:21<00:14,  2.94it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:21<00:14,  2.92it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:22<00:13,  2.93it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:22<00:13,  2.95it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:22<00:12,  2.97it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:23<00:12,  2.97it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:23<00:12,  2.96it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:23<00:11,  2.92it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:24<00:12,  2.69it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:25<00:17,  1.89it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:26<00:22,  1.44it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:27<00:23,  1.34it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:28<00:24,  1.23it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:28<00:20,  1.43it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:28<00:16,  1.67it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:29<00:14,  1.90it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:29<00:12,  2.10it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:29<00:10,  2.29it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:30<00:09,  2.41it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:30<00:09,  2.52it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:30<00:08,  2.58it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:31<00:08,  2.62it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:31<00:07,  2.69it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:32<00:06,  2.74it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:32<00:06,  2.80it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:32<00:05,  2.85it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:33<00:05,  2.87it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:33<00:05,  2.89it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:33<00:04,  2.86it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:34<00:04,  2.86it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:34<00:04,  2.78it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:34<00:04,  2.72it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:35<00:03,  2.72it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:35<00:03,  2.71it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:35<00:02,  2.76it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:36<00:02,  2.75it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:36<00:02,  2.76it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:37<00:01,  2.81it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:37<00:01,  2.85it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:37<00:01,  2.87it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:38<00:00,  2.91it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:38<00:00,  2.93it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:38<00:00,  2.92it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:38<00:00,  2.58it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 5e-08, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 977

  0%|          | 0/100 [00:00<?, ?it/s]
 35%|███▌      | 35/100 [00:00<00:00, 345.44it/s]
 70%|███████   | 70/100 [00:00<00:00, 347.09it/s]
100%|██████████| 100/100 [00:00<00:00, 349.83it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:55,  1.78it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:52,  1.85it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:52,  1.84it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:51,  1.86it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:52,  1.81it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:51,  1.83it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:50,  1.85it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:49,  1.88it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:04<00:48,  1.89it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:05<00:48,  1.85it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:05<00:47,  1.88it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:06<00:46,  1.90it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:06<00:45,  1.90it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:07<00:44,  1.91it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:08<00:50,  1.68it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:09<00:55,  1.51it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:09<00:59,  1.40it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:10<01:02,  1.32it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:11<01:02,  1.29it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:12<01:03,  1.25it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:13<01:04,  1.22it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:14<01:04,  1.20it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<01:04,  1.19it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:15<01:04,  1.18it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:16<01:03,  1.17it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:17<01:03,  1.17it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:18<01:02,  1.17it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:19<01:02,  1.16it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:20<01:01,  1.15it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:20<00:57,  1.21it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:21<00:50,  1.36it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:21<00:45,  1.50it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:22<00:42,  1.59it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:23<00:39,  1.68it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:23<00:37,  1.75it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:24<00:35,  1.81it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:24<00:33,  1.86it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:25<00:32,  1.88it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:25<00:37,  1.63it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:26<00:41,  1.46it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:27<00:43,  1.37it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:28<00:44,  1.32it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:28<00:40,  1.42it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:29<00:36,  1.54it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:29<00:33,  1.65it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:30<00:31,  1.72it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:31<00:29,  1.79it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:31<00:28,  1.84it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:32<00:27,  1.88it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:32<00:26,  1.91it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:33<00:25,  1.91it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:33<00:24,  1.93it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:34<00:24,  1.95it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:34<00:23,  1.96it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:35<00:23,  1.95it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:35<00:23,  1.90it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:36<00:22,  1.88it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:36<00:22,  1.89it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:37<00:21,  1.90it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:37<00:21,  1.90it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:38<00:20,  1.90it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:38<00:20,  1.89it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:39<00:20,  1.83it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:39<00:19,  1.83it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:40<00:18,  1.86it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:41<00:18,  1.88it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:41<00:17,  1.89it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:42<00:16,  1.90it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:42<00:16,  1.92it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:43<00:15,  1.92it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:43<00:15,  1.92it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:44<00:14,  1.91it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:44<00:14,  1.92it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:45<00:13,  1.90it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:45<00:13,  1.92it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:46<00:13,  1.84it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:46<00:13,  1.69it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:47<00:12,  1.73it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:47<00:11,  1.89it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:48<00:09,  2.00it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:48<00:09,  2.08it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:49<00:08,  2.08it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:49<00:07,  2.19it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:50<00:07,  2.26it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:50<00:06,  2.31it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:50<00:05,  2.34it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:51<00:05,  2.37it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:51<00:05,  2.27it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:52<00:04,  2.24it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:52<00:04,  2.25it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:53<00:03,  2.28it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:53<00:03,  2.29it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:54<00:03,  2.24it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:54<00:02,  2.24it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:54<00:02,  2.27it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:55<00:01,  2.08it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:56<00:02,  1.50it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:57<00:01,  1.26it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:58<00:00,  1.11it/s]