qwen2.5-3b - 开始实验

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:05<00:05,  5.94s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.39s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.62s/it]
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 39%|███▉      | 39/100 [00:00<00:00, 381.69it/s]
 78%|███████▊  | 78/100 [00:00<00:00, 382.75it/s]
100%|██████████| 100/100 [00:00<00:00, 383.34it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:50,  1.97it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:17,  5.63it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:10,  8.69it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:08, 11.11it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:00<00:07, 12.93it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:06, 14.32it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:05, 15.36it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:05, 16.10it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:01<00:04, 16.62it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:01<00:04, 17.02it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:01<00:04, 17.28it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:01<00:04, 17.45it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:01<00:04, 17.47it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:01<00:04, 17.61it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:02<00:04, 17.72it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:02<00:03, 17.81it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:02<00:03, 17.86it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:02<00:03, 17.93it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:02<00:03, 17.93it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:02<00:03, 17.82it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:02<00:03, 17.86it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:02<00:03, 17.88it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:02<00:03, 17.91it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:03<00:02, 17.96it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:03<00:02, 17.99it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:03<00:02, 18.01it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:03<00:02, 17.99it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:03<00:02, 17.85it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:03<00:02, 17.87it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:03<00:02, 17.89it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:03<00:02, 17.95it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:03<00:02, 17.94it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:04<00:01, 17.93it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:04<00:01, 17.97it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:04<00:01, 17.91it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:04<00:01, 17.97it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:04<00:01, 18.01it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:04<00:01, 18.06it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:04<00:01, 18.06it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:04<00:01, 18.06it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:04<00:01, 18.05it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:05<00:00, 18.01it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:05<00:00, 18.03it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:05<00:00, 18.12it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:05<00:00, 18.19it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:05<00:00, 18.23it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:05<00:00, 18.25it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:05<00:00, 18.30it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:05<00:00, 18.34it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:05<00:00, 18.29it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:06<00:00, 16.61it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
lambada_openai acc: 0.6300
qwen2.5-3b - 基准准确率: 0.6300
qwen2.5-3b - 测试错误数目: 100

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.02s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.07s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.22s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 100 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试错误数目: 500

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.13s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.06s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.22s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 500 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试错误数目: 1000

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.48s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.68s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.80s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 1000 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试错误数目: 3000

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.96s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  2.96s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.11s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 3000 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试错误数目: 5000

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.91s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.07s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.20s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 5000 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 实验完成，结果保存到: results/qwen2.5-3b_fixed_errors_results_20250722_214538.json
