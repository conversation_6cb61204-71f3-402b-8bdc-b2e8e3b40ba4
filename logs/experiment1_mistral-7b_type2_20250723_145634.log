Traceback (most recent call last):
  File "<string>", line 3, in <module>
  File "/root/lanyun-tmp/error/utils/eval_model.py", line 1, in <module>
    import lm_eval
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/lm_eval/__init__.py", line 4, in <module>
    from .evaluator import evaluate, simple_evaluate
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/lm_eval/evaluator.py", line 12, in <module>
    import lm_eval.api.metrics
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/lm_eval/api/metrics.py", line 12, in <module>
    from lm_eval.api.registry import register_aggregation, register_metric
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/lm_eval/api/registry.py", line 4, in <module>
    import evaluate as hf_evaluate
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/evaluate/__init__.py", line 29, in <module>
    from .evaluation_suite import EvaluationSuite
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/evaluate/evaluation_suite/__init__.py", line 10, in <module>
    from ..evaluator import evaluator
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/evaluate/evaluator/__init__.py", line 17, in <module>
    from transformers.pipelines import SUPPORTED_TASKS as SUPPORTED_PIPELINE_TASKS
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/transformers/pipelines/__init__.py", line 26, in <module>
    from ..image_processing_utils import BaseImageProcessor
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/transformers/image_processing_utils.py", line 22, in <module>
    from .image_transforms import center_crop, normalize, rescale
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/transformers/image_transforms.py", line 22, in <module>
    from .image_utils import (
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/torchvision/__init__.py", line 6, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/torch/library.py", line 1023, in register
    use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/torch/library.py", line 214, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/root/miniconda/envs/exp/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):
RuntimeError: operator torchvision::nms does not exist
