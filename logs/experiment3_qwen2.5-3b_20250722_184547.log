开始加载模型: qwen2.5-3b
qwen2.5-3b - 错误数目列表: [100, 500, 1000, 3000, 5000, 8000]
qwen2.5-3b - Bit范围: 16
qwen2.5-3b - Type01模式: 3
qwen2.5-3b - 测试无错误基准...

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:06<00:06,  6.10s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.46s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.71s/it]
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 378.04it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 378.33it/s]
100%|██████████| 100/100 [00:00<00:00, 378.94it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:44,  2.22it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:17,  5.63it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:11,  8.05it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:00<00:09,  9.71it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:08, 10.90it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:07, 11.71it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:07, 12.28it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:06, 12.70it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:01<00:06, 12.93it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:01<00:06, 13.15it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:01<00:05, 13.27it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:02<00:05, 13.37it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:02<00:05, 13.46it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:02<00:05, 13.52it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:02<00:05, 13.52it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:02<00:05, 13.57it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:02<00:04, 13.59it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:02<00:04, 13.63it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:03<00:04, 13.63it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:03<00:04, 13.62it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:03<00:04, 13.65it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:03<00:04, 13.60it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:03<00:04, 13.61it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:03<00:03, 13.63it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:03<00:03, 13.68it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:04<00:03, 13.70it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:04<00:03, 13.69it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:04<00:03, 13.62it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:04<00:03, 13.56it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:04<00:03, 13.57it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:04<00:02, 13.62it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:05<00:02, 13.57it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:05<00:02, 13.58it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:05<00:02, 13.58it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:05<00:02, 13.58it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:05<00:02, 13.60it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:05<00:01, 13.57it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:05<00:01, 13.61it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:06<00:01, 13.65it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:06<00:01, 13.64it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:06<00:01, 13.68it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:06<00:01, 13.66it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:06<00:01, 13.67it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:06<00:00, 13.69it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:06<00:00, 13.69it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:07<00:00, 13.72it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:07<00:00, 13.75it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:07<00:00, 13.73it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:07<00:00, 13.76it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:07<00:00, 13.80it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:07<00:00, 12.95it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
lambada_openai acc: 0.6300
qwen2.5-3b - 基准准确率: 0.6300
qwen2.5-3b - 测试 error_number: 100, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.17s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.15s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.30s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 100 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试 error_number: 500, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.96s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.15s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 500 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试 error_number: 1000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.14s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.07s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.23s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 1000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试 error_number: 3000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  2.92s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.08s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 3000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试 error_number: 5000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.28s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.44s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.56s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 5000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 测试 error_number: 8000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.59s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.85s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.96s/it]
模型权重位数: torch.float16
qwen2.5-3b - 错误数目 8000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 395000152064 bytes. Error code 12 (Cannot allocate memory)
qwen2.5-3b - 固定错误数目实验完成，结果保存到: results/qwen2.5-3b_fixed_errors_results_20250722_184547.json

qwen2.5-3b - 实验结果摘要:
错误数目	准确率	准确率下降
0		0.6300	0.0000
