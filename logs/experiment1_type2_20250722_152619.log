`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lamba<PERSON>_<PERSON>ai from None to 0
开始加载模型...
type01=2, error_rate: 1e-10, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 370.15it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 372.34it/s]
100%|██████████| 100/100 [00:00<00:00, 370.82it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:47,  2.09it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:27,  3.56it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:21,  4.50it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:18,  5.26it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:16,  5.81it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:15,  6.20it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:14,  6.47it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:13,  6.59it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:13,  6.75it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:13,  6.87it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:12,  6.95it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:12,  6.97it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:12,  7.04it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:12,  7.08it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:02<00:11,  7.10it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:11,  7.11it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:11,  7.13it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:11,  7.13it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:11,  7.15it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:03<00:11,  7.14it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:03<00:11,  7.15it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:03<00:10,  7.17it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:03<00:10,  7.16it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:03<00:10,  7.15it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:10,  7.17it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:10,  7.18it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:04<00:10,  7.18it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:04<00:10,  7.17it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:04<00:09,  7.18it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:04<00:09,  7.15it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:04<00:09,  7.15it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:09,  7.15it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:09,  7.14it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:05<00:09,  7.16it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:05<00:09,  7.16it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:05<00:08,  7.17it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:05<00:08,  7.16it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:05<00:08,  7.17it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:05<00:08,  7.16it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:08,  7.15it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:06<00:08,  7.12it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:06<00:08,  7.15it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:06<00:07,  7.16it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:06<00:07,  7.17it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:06<00:07,  7.15it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:06<00:07,  7.16it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:06<00:07,  7.17it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:07<00:07,  7.16it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:07<00:07,  7.16it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:07<00:06,  7.16it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:07<00:06,  7.16it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:07<00:06,  7.18it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:07<00:06,  7.11it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:07<00:06,  7.13it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:08<00:06,  7.15it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:08<00:06,  7.17it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:08<00:05,  7.17it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:08<00:05,  7.20it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:08<00:05,  7.20it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:08<00:05,  7.18it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:08<00:05,  7.19it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:09<00:05,  7.19it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:09<00:05,  7.19it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:09<00:05,  7.20it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:09<00:04,  7.19it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:09<00:04,  7.20it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:09<00:04,  7.21it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:09<00:04,  7.21it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:09<00:04,  7.21it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:10<00:04,  7.21it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:10<00:04,  7.21it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:10<00:03,  7.20it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:10<00:03,  7.20it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:10<00:03,  7.20it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:10<00:03,  7.20it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:10<00:03,  7.12it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:11<00:03,  7.14it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:11<00:03,  7.17it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:11<00:02,  7.20it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:11<00:02,  7.22it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:11<00:02,  7.22it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:11<00:02,  7.21it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:11<00:02,  7.21it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:12<00:02,  7.22it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:12<00:02,  7.23it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:12<00:01,  7.23it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:12<00:01,  7.22it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:12<00:01,  7.21it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:12<00:01,  7.22it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:12<00:01,  7.22it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:13<00:01,  7.22it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:13<00:01,  7.21it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:13<00:00,  7.23it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:13<00:00,  7.24it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:13<00:00,  7.25it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:13<00:00,  7.25it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:13<00:00,  7.24it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:14<00:00,  7.21it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:14<00:00,  7.21it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:14<00:00,  7.22it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:14<00:00,  7.00it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=2, error_rate: 1e-10, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 370.92it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 372.53it/s]
100%|██████████| 100/100 [00:00<00:00, 369.84it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:31,  3.19it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:30,  3.21it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:30,  3.22it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:29,  3.22it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:29,  3.21it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:29,  3.22it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:28,  3.22it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:28,  3.22it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:28,  3.22it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:28,  3.20it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:27,  3.20it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:27,  3.21it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:27,  3.18it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:27,  3.18it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:26,  3.19it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:26,  3.20it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:05<00:25,  3.20it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:05<00:25,  3.21it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:25,  3.21it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:06<00:24,  3.22it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:06<00:24,  3.22it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:24,  3.22it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:07<00:23,  3.23it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:07<00:23,  3.24it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:07<00:23,  3.22it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:08<00:22,  3.23it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:08<00:22,  3.23it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:08<00:22,  3.23it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:09<00:21,  3.23it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:09<00:21,  3.23it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:09<00:21,  3.24it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:09<00:20,  3.24it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:10<00:20,  3.25it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:10<00:20,  3.25it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:10<00:19,  3.25it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:11<00:19,  3.23it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:11<00:19,  3.23it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:11<00:19,  3.24it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:12<00:18,  3.25it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:12<00:18,  3.25it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:12<00:18,  3.25it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:13<00:17,  3.26it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:13<00:17,  3.25it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:13<00:17,  3.26it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:13<00:16,  3.25it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:14<00:16,  3.25it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:14<00:16,  3.26it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:14<00:15,  3.26it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:15<00:15,  3.26it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:15<00:15,  3.25it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:15<00:15,  3.25it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:16<00:14,  3.26it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:16<00:14,  3.26it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:16<00:14,  3.27it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:17<00:13,  3.26it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:17<00:13,  3.26it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:17<00:13,  3.27it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:17<00:12,  3.27it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:18<00:12,  3.26it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:18<00:12,  3.26it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:18<00:11,  3.26it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:19<00:11,  3.26it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:19<00:11,  3.26it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:19<00:11,  3.27it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:20<00:10,  3.26it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:20<00:10,  3.26it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:20<00:10,  3.27it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:20<00:09,  3.27it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:21<00:09,  3.25it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:21<00:09,  3.25it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:21<00:08,  3.26it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:22<00:08,  3.26it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:22<00:08,  3.26it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:22<00:07,  3.26it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:23<00:07,  3.26it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:23<00:07,  3.26it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:23<00:07,  3.26it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:24<00:06,  3.26it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:24<00:06,  3.26it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:24<00:06,  3.27it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:24<00:05,  3.26it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:25<00:05,  3.26it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:25<00:05,  3.27it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:25<00:04,  3.26it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:26<00:04,  3.26it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:26<00:04,  3.26it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:26<00:03,  3.25it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:27<00:03,  3.25it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:27<00:03,  3.26it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:27<00:03,  3.26it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:28<00:02,  3.26it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:28<00:02,  3.26it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:28<00:02,  3.25it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:28<00:01,  3.24it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:29<00:01,  3.25it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:29<00:01,  3.26it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:29<00:00,  3.26it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:30<00:00,  3.26it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:30<00:00,  3.27it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:30<00:00,  3.27it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:30<00:00,  3.25it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=2, error_rate: 1e-10, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.05it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 373.05it/s]
100%|██████████| 100/100 [00:00<00:00, 371.90it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:43,  2.28it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:42,  2.29it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:42,  2.30it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:41,  2.29it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:41,  2.30it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:40,  2.30it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:03<00:40,  2.30it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:39,  2.30it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:39,  2.30it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:39,  2.31it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:04<00:38,  2.31it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:38,  2.31it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:37,  2.31it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:06<00:38,  2.23it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:37,  2.26it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:06<00:36,  2.27it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:36,  2.28it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:07<00:35,  2.30it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:08<00:35,  2.30it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:08<00:34,  2.30it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:09<00:34,  2.31it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:09<00:33,  2.31it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:10<00:33,  2.31it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:32,  2.31it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:10<00:32,  2.32it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:11<00:31,  2.32it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:11<00:31,  2.32it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:12<00:31,  2.32it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:12<00:30,  2.32it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:13<00:30,  2.32it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:13<00:29,  2.32it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:13<00:29,  2.32it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:14<00:28,  2.32it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:14<00:28,  2.33it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:15<00:27,  2.33it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:15<00:27,  2.32it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:16<00:27,  2.32it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:16<00:26,  2.31it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:16<00:26,  2.31it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:17<00:25,  2.32it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:25,  2.32it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:18<00:24,  2.33it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:18<00:24,  2.32it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:19<00:24,  2.32it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:19<00:23,  2.32it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:19<00:23,  2.32it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:20<00:22,  2.32it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:20<00:22,  2.32it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:21<00:21,  2.32it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:21<00:21,  2.32it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:22<00:21,  2.33it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:22<00:20,  2.33it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:22<00:20,  2.33it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:23<00:19,  2.33it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:23<00:19,  2.33it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:24<00:18,  2.33it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:24<00:18,  2.33it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:25<00:18,  2.33it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:25<00:17,  2.33it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:25<00:17,  2.33it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:26<00:16,  2.33it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:26<00:16,  2.33it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:27<00:15,  2.33it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:27<00:15,  2.32it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:28<00:15,  2.32it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:28<00:14,  2.33it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:28<00:14,  2.33it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:29<00:13,  2.33it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:29<00:13,  2.33it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:30<00:12,  2.33it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:30<00:12,  2.33it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:31<00:12,  2.33it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:31<00:11,  2.33it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:31<00:11,  2.32it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:32<00:10,  2.32it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:32<00:10,  2.33it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:33<00:09,  2.33it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:33<00:09,  2.33it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:34<00:09,  2.33it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:34<00:08,  2.33it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:34<00:08,  2.33it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:35<00:07,  2.33it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:35<00:07,  2.33it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:36<00:06,  2.33it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:36<00:06,  2.33it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:37<00:06,  2.33it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:37<00:05,  2.33it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:37<00:05,  2.33it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:38<00:04,  2.32it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:38<00:04,  2.32it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:39<00:03,  2.32it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:39<00:03,  2.32it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:40<00:03,  2.32it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:40<00:02,  2.32it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:40<00:02,  2.33it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:41<00:01,  2.33it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:41<00:01,  2.33it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:42<00:00,  2.33it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:42<00:00,  2.33it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:43<00:00,  2.32it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:43<00:00,  2.32it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=2, error_rate: 1e-10, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 371.88it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 372.07it/s]
100%|██████████| 100/100 [00:00<00:00, 373.22it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:42,  2.35it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:41,  2.36it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:40,  2.37it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:40,  2.37it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:39,  2.38it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:39,  2.38it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:38,  2.39it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:38,  2.37it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:38,  2.38it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:37,  2.39it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:04<00:37,  2.39it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:36,  2.40it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:36,  2.38it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:05<00:36,  2.38it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:35,  2.39it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:06<00:35,  2.39it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:34,  2.40it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:07<00:34,  2.40it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:07<00:33,  2.39it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:08<00:33,  2.39it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:08<00:33,  2.39it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:09<00:32,  2.39it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:09<00:32,  2.40it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:31,  2.40it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:10<00:31,  2.40it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:10<00:30,  2.39it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:11<00:30,  2.39it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:11<00:30,  2.39it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:12<00:29,  2.40it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:12<00:29,  2.39it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:12<00:28,  2.40it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:13<00:28,  2.40it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:13<00:27,  2.41it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:14<00:27,  2.41it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:14<00:27,  2.40it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:15<00:26,  2.40it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:15<00:26,  2.40it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:15<00:25,  2.40it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:16<00:25,  2.40it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:16<00:24,  2.40it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:24,  2.40it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:17<00:24,  2.41it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:17<00:23,  2.41it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:18<00:23,  2.41it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:18<00:22,  2.42it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:19<00:22,  2.40it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:19<00:22,  2.40it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:20<00:21,  2.40it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:20<00:21,  2.40it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:20<00:20,  2.41it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:21<00:20,  2.40it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:21<00:19,  2.40it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:22<00:19,  2.40it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:22<00:19,  2.40it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:22<00:18,  2.40it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:23<00:18,  2.40it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:23<00:17,  2.41it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:24<00:17,  2.41it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:24<00:17,  2.41it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:25<00:16,  2.41it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:25<00:16,  2.41it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:25<00:15,  2.40it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:26<00:15,  2.41it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:26<00:14,  2.41it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:27<00:14,  2.41it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:27<00:14,  2.40it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:27<00:13,  2.39it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:28<00:13,  2.40it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:28<00:12,  2.40it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:29<00:12,  2.40it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:29<00:12,  2.40it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:30<00:11,  2.40it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:30<00:11,  2.39it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:30<00:10,  2.39it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:31<00:10,  2.40it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:31<00:09,  2.40it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:32<00:09,  2.41it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:32<00:09,  2.41it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:32<00:08,  2.42it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:33<00:08,  2.41it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:33<00:07,  2.41it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:34<00:07,  2.41it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:34<00:07,  2.41it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:35<00:06,  2.41it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:35<00:06,  2.41it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:35<00:05,  2.40it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:36<00:05,  2.40it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:36<00:04,  2.40it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:37<00:04,  2.40it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:37<00:04,  2.41it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:37<00:03,  2.40it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:38<00:03,  2.40it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:38<00:02,  2.40it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:39<00:02,  2.40it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:39<00:02,  2.41it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:40<00:01,  2.41it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:40<00:01,  2.42it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:40<00:00,  2.42it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:41<00:00,  2.41it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:41<00:00,  2.41it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:41<00:00,  2.40it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=2, error_rate: 1e-10, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.82it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 373.25it/s]
100%|██████████| 100/100 [00:00<00:00, 372.02it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:08,  1.45it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:07,  1.45it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:02<01:06,  1.45it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:06,  1.45it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:05,  1.45it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:04<01:04,  1.45it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<01:04,  1.45it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<01:03,  1.45it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:06<01:02,  1.45it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<01:02,  1.45it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:07<01:01,  1.45it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:08<01:00,  1.46it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:08<00:59,  1.46it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:09<00:58,  1.46it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:10<00:58,  1.46it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:10<00:57,  1.46it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:11<00:56,  1.47it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:12<00:56,  1.46it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:13<00:55,  1.46it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:13<00:54,  1.46it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:14<00:53,  1.46it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:15<00:53,  1.47it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:15<00:52,  1.47it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:16<00:51,  1.47it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:17<00:51,  1.47it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:17<00:50,  1.47it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:18<00:49,  1.47it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:19<00:49,  1.47it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:19<00:48,  1.47it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:20<00:47,  1.47it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:21<00:47,  1.47it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:21<00:46,  1.47it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:22<00:45,  1.47it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:23<00:45,  1.47it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:23<00:44,  1.47it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:24<00:43,  1.47it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:25<00:43,  1.46it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:25<00:42,  1.46it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:26<00:41,  1.46it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:27<00:41,  1.43it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:28<00:40,  1.45it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:28<00:39,  1.45it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:29<00:39,  1.46it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:30<00:38,  1.46it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:30<00:37,  1.47it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:31<00:36,  1.47it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:32<00:36,  1.47it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:32<00:35,  1.47it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:33<00:34,  1.47it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:34<00:33,  1.47it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:34<00:33,  1.47it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:35<00:32,  1.47it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:36<00:31,  1.47it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:36<00:31,  1.47it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:37<00:30,  1.47it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:38<00:29,  1.48it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:38<00:29,  1.48it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:39<00:28,  1.48it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:40<00:27,  1.47it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:40<00:27,  1.47it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:41<00:26,  1.47it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:42<00:25,  1.48it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:43<00:25,  1.48it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:43<00:24,  1.48it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:44<00:23,  1.48it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:45<00:23,  1.48it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:45<00:22,  1.48it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:46<00:21,  1.48it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:47<00:20,  1.48it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:47<00:20,  1.48it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:48<00:19,  1.48it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:49<00:18,  1.48it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:49<00:18,  1.48it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:50<00:17,  1.48it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:51<00:16,  1.48it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:51<00:16,  1.48it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:52<00:15,  1.48it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:53<00:14,  1.48it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:53<00:14,  1.48it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:54<00:13,  1.48it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:55<00:12,  1.48it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:55<00:12,  1.47it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:56<00:11,  1.47it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:57<00:10,  1.47it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:57<00:10,  1.47it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:58<00:09,  1.47it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:59<00:08,  1.47it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:59<00:08,  1.47it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [01:00<00:07,  1.48it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [01:01<00:06,  1.48it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [01:01<00:06,  1.47it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [01:02<00:05,  1.48it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [01:03<00:04,  1.47it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [01:04<00:04,  1.48it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [01:04<00:03,  1.48it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [01:05<00:02,  1.48it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [01:06<00:02,  1.47it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [01:06<00:01,  1.48it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [01:07<00:00,  1.48it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:08<00:00,  1.48it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [01:08<00:00,  1.47it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=2, error_rate: 1e-10, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 377.59it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 377.23it/s]
100%|██████████| 100/100 [00:00<00:00, 377.68it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:20,  1.22it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:19,  1.23it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:02<01:19,  1.23it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:03<01:20,  1.20it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:04<01:18,  1.21it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:04<01:17,  1.22it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:05<01:15,  1.23it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:06<01:15,  1.23it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:07<01:14,  1.22it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:08<01:13,  1.23it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:08<01:12,  1.23it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:09<01:11,  1.23it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:10<01:10,  1.24it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:11<01:09,  1.24it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:12<01:08,  1.23it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:13<01:07,  1.24it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:13<01:07,  1.24it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:14<01:06,  1.24it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:15<01:05,  1.24it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:16<01:04,  1.24it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:17<01:03,  1.24it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:17<01:02,  1.24it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:18<01:01,  1.24it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:19<01:01,  1.24it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:20<01:00,  1.24it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:21<00:59,  1.24it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:21<00:58,  1.24it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:22<00:58,  1.24it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:23<00:57,  1.24it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:24<00:56,  1.24it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:25<00:55,  1.24it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:25<00:54,  1.24it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:26<00:53,  1.25it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:27<00:52,  1.25it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:28<00:52,  1.24it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:29<00:51,  1.25it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:29<00:50,  1.25it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:30<00:49,  1.25it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:31<00:48,  1.25it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:32<00:48,  1.25it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:33<00:47,  1.25it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:33<00:46,  1.25it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:34<00:45,  1.25it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:35<00:44,  1.25it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:36<00:44,  1.25it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:37<00:43,  1.24it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:37<00:42,  1.25it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:38<00:41,  1.25it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:39<00:41,  1.24it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:40<00:40,  1.25it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:41<00:39,  1.25it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:41<00:38,  1.25it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:42<00:37,  1.25it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:43<00:36,  1.25it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:44<00:36,  1.25it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:45<00:35,  1.25it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:45<00:34,  1.25it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:46<00:33,  1.25it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:47<00:32,  1.25it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:48<00:32,  1.24it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:49<00:31,  1.24it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:49<00:30,  1.25it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:50<00:29,  1.25it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:51<00:28,  1.25it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:52<00:28,  1.25it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:53<00:27,  1.25it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:53<00:26,  1.25it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:54<00:25,  1.24it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:55<00:24,  1.24it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:56<00:24,  1.25it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:57<00:23,  1.25it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:58<00:22,  1.25it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:58<00:21,  1.25it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:59<00:20,  1.24it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [01:00<00:20,  1.24it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [01:01<00:19,  1.24it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [01:02<00:18,  1.25it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [01:02<00:17,  1.25it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [01:03<00:16,  1.25it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [01:04<00:16,  1.25it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [01:05<00:15,  1.25it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [01:06<00:14,  1.25it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [01:06<00:13,  1.25it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [01:07<00:12,  1.25it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [01:08<00:12,  1.25it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [01:09<00:11,  1.25it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [01:10<00:10,  1.25it/s]