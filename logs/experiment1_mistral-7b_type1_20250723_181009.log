开始加载模型: mistral-7b
mistral-7b - type01=1, error_rate: 1e-12, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-12, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-12, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-12, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-11, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-11, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-11, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-11, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-10, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-10, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-10, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-10, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-09, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-09, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-09, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-09, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-08, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-08, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-08, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-08, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-07, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-07, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 2
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 4
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 6
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 8
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 10
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 12
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 14
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 5e-07, bit_range: 15
Error in mistral-7b type01=1, error_rate=5e-07, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 2
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=2: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 4
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=4: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 6
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=6: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 8
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=8: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 10
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=10: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 12
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=12: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 14
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=14: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b - type01=1, error_rate: 1e-06, bit_range: 15
Error in mistral-7b type01=1, error_rate=1e-06, bit_range=15: 
 requires the protobuf library but it was not found in your environment. Check out the instructions on the
installation page of its repo: https://github.com/protocolbuffers/protobuf/tree/master/python#installation and follow the ones
that match your environment. Please note that you may need to restart your runtime after installation.

mistral-7b Type01=1 实验完成，结果保存到: results/mistral-7b_type1_results_20250723_181009.json
