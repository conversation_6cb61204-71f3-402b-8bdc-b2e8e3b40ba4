`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lamba<PERSON>_openai from None to 0
开始加载模型: llama-3.2-1b
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 368.86it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 370.90it/s]
100%|██████████| 100/100 [00:00<00:00, 371.45it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:48,  2.05it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:27,  3.63it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:20,  4.69it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:17,  5.58it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:15,  6.21it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:14,  6.66it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:13,  7.01it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:12,  7.19it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:12,  7.34it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  7.51it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  7.59it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:11,  7.69it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:11,  7.76it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:11,  7.81it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:02<00:10,  7.84it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:10,  7.86it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:10,  7.90it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:10,  7.90it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:10,  7.90it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:10,  7.92it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:03<00:09,  7.91it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:03<00:09,  7.93it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:03<00:09,  7.93it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:03<00:09,  7.89it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:09,  7.90it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:09,  7.93it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:09,  7.93it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:09,  7.92it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:04<00:08,  7.93it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:04<00:08,  7.90it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:04<00:08,  7.90it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:08,  7.91it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:08,  7.90it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  7.91it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:08,  7.93it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:08,  7.90it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:05<00:07,  7.91it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:05<00:07,  7.90it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:05<00:07,  7.92it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:07,  7.91it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  7.92it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  7.91it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:07,  7.87it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:07,  7.88it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:06<00:06,  7.86it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:06<00:06,  7.85it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:06<00:06,  7.86it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:06<00:06,  7.87it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  7.85it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  7.78it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:06,  7.75it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:06<00:06,  7.81it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:07<00:06,  7.79it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:07<00:05,  7.85it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:07<00:05,  7.88it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:07<00:05,  7.87it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  7.84it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  7.84it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  7.81it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:07<00:05,  7.81it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:08<00:05,  7.77it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:08<00:04,  7.77it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:08<00:04,  7.81it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:08<00:04,  7.86it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.91it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  7.93it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  7.95it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:09<00:04,  7.95it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:09<00:03,  7.96it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:09<00:03,  7.69it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:09<00:03,  7.76it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:09<00:03,  7.80it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  7.83it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  7.83it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  7.85it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:10<00:03,  7.83it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:10<00:02,  7.82it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:10<00:02,  7.86it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:10<00:02,  7.90it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:10<00:02,  7.91it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  7.81it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.80it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  7.85it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:11<00:02,  7.91it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:11<00:01,  7.94it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:11<00:01,  7.94it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:11<00:01,  7.93it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:11<00:01,  7.95it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  7.97it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  7.99it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  8.03it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:12<00:01,  7.95it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:12<00:00,  7.98it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:12<00:00,  7.96it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:12<00:00,  7.95it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  7.94it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  7.91it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  7.97it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:13<00:00,  7.97it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:13<00:00,  7.66it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 372.32it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 373.55it/s]
100%|██████████| 100/100 [00:00<00:00, 372.66it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:20,  4.85it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:19,  4.92it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:19,  4.93it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:19,  4.95it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:19,  4.95it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:18,  4.97it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:18,  4.98it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:18,  4.98it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:18,  4.99it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:18,  4.99it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:17,  4.99it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:17,  4.99it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:17,  4.96it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:17,  4.97it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:03<00:17,  4.99it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:03<00:16,  4.98it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:16,  4.99it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:16,  4.99it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:16,  5.00it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:04<00:15,  5.01it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:04<00:15,  5.00it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:04<00:15,  5.00it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:15,  4.97it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:04<00:15,  4.99it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:05<00:14,  5.00it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:05<00:14,  5.01it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:05<00:14,  5.02it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:05<00:14,  5.02it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:05<00:14,  5.02it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:06<00:13,  5.03it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:06<00:13,  5.03it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:06<00:13,  5.02it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:06<00:13,  5.02it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:06<00:13,  5.02it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:07<00:13,  4.98it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:07<00:12,  4.96it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:07<00:12,  4.92it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:07<00:12,  4.89it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:07<00:12,  4.87it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:08<00:12,  4.86it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:08<00:12,  4.85it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:08<00:11,  4.85it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:08<00:11,  4.86it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:08<00:11,  4.89it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:09<00:11,  4.92it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:09<00:10,  4.96it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:09<00:10,  4.97it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:09<00:10,  4.99it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:09<00:10,  4.99it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:10<00:10,  4.96it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:10<00:09,  4.97it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:10<00:09,  4.98it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:10<00:09,  5.00it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:10<00:09,  5.00it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:11<00:08,  5.01it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:11<00:08,  5.02it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:11<00:08,  5.02it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:11<00:08,  5.03it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:11<00:08,  5.03it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:12<00:07,  5.02it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:12<00:07,  5.02it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:12<00:07,  5.02it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:12<00:07,  5.01it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:12<00:07,  5.01it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:13<00:06,  5.02it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:13<00:06,  5.02it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:13<00:06,  5.00it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:13<00:06,  5.01it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:13<00:06,  5.01it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:14<00:05,  5.00it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:14<00:05,  5.01it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:14<00:05,  5.02it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:14<00:05,  5.02it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:14<00:05,  5.02it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:15<00:04,  5.03it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:15<00:04,  5.02it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:15<00:04,  5.01it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:15<00:04,  5.01it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:15<00:04,  5.01it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:16<00:03,  5.01it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:16<00:03,  5.01it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:16<00:03,  5.02it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:16<00:03,  5.02it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:16<00:03,  5.01it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:17<00:02,  5.00it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:17<00:02,  4.99it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:17<00:02,  4.93it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:17<00:02,  4.87it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:17<00:02,  4.85it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:18<00:02,  4.84it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:18<00:01,  4.79it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:18<00:01,  4.79it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:18<00:01,  4.80it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:18<00:01,  4.83it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:19<00:01,  4.89it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:19<00:00,  4.94it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:19<00:00,  4.97it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:19<00:00,  4.98it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:19<00:00,  4.98it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.99it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.97it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
llama-3.2-1b - type01=3, error_rate: 1e-10, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 374.71it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 377.63it/s]
100%|██████████| 100/100 [00:00<00:00, 376.94it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:31,  3.13it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:28,  3.39it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:27,  3.48it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:27,  3.53it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:26,  3.55it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:26,  3.57it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:25,  3.59it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:25,  3.58it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:02<00:25,  3.58it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:02<00:25,  3.59it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:24,  3.57it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:03<00:24,  3.58it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:03<00:24,  3.58it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:03<00:24,  3.58it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:04<00:23,  3.59it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:04<00:23,  3.59it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:04<00:23,  3.59it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:05<00:22,  3.59it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:05<00:22,  3.60it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:05<00:22,  3.60it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:05<00:21,  3.60it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:06<00:21,  3.60it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:06<00:21,  3.56it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:06<00:21,  3.58it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:07<00:20,  3.59it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:07<00:20,  3.58it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:07<00:20,  3.59it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:07<00:20,  3.59it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:08<00:19,  3.58it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:08<00:19,  3.56it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:08<00:19,  3.56it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:08<00:19,  3.56it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:09<00:18,  3.58it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:09<00:18,  3.61it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:09<00:17,  3.63it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:10<00:17,  3.64it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:10<00:17,  3.65it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:10<00:16,  3.65it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:10<00:16,  3.67it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:11<00:16,  3.66it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:11<00:16,  3.65it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:11<00:15,  3.65it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:11<00:15,  3.65it/s]