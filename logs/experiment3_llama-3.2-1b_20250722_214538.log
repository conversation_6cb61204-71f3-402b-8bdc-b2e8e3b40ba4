`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
llama-3.2-1b - 开始实验
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 374.57it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 377.42it/s]
100%|██████████| 100/100 [00:00<00:00, 377.93it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:43,  2.26it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:08, 10.85it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:00<00:05, 17.86it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:00<00:03, 23.29it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:00<00:03, 27.30it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:00<00:02, 30.39it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:01<00:02, 32.64it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:01<00:02, 34.04it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:01<00:01, 35.01it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:01<00:01, 35.82it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:01<00:01, 36.34it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:01<00:01, 36.86it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:01<00:01, 37.36it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:01<00:01, 36.93it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:01<00:01, 37.25it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:02<00:01, 37.65it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:02<00:00, 37.84it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:02<00:00, 37.80it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:02<00:00, 38.09it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:02<00:00, 38.20it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:02<00:00, 38.04it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:02<00:00, 38.26it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:02<00:00, 38.57it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:02<00:00, 38.59it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:02<00:00, 38.58it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:03<00:00, 32.67it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
