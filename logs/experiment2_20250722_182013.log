/root/lanyun-tmp/error/utils/distribute.py:23: UserWarning: Tight layout not applied. tight_layout cannot make Axes height small enough to accommodate all Axes decorations.
  plt.tight_layout()
/root/lanyun-tmp/error/utils/distribute.py:55: UserWarning: Tight layout not applied. tight_layout cannot make Axes height small enough to accommodate all Axes decorations.
  plt.tight_layout()
开始加载模型进行分布分析...
模型权重位数: torch.float16
分析权重分布...
权重分布分析完成
分析激活分布...
处理样本 1/5: The quick brown fox jumps over the lazy dog....
处理样本 2/5: Hello world, this is a test sentence....
处理样本 3/5: Machine learning is transforming the world....
处理样本 4/5: Natural language processing enables computers to u...
处理样本 5/5: Deep learning models require large amounts of data...
激活分布分析完成
权重分布图已保存
分布分析完成！
生成的文件：
- 权重分布图: results/weight_distribution_20250722_182013.png
- 激活分布图: results/activation_distribution_sample_*_20250722_182013.png
