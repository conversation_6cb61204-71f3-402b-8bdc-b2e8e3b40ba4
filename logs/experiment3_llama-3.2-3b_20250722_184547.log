开始加载模型: llama-3.2-3b
llama-3.2-3b - 错误数目列表: [100, 500, 1000, 3000, 5000, 8000]
llama-3.2-3b - Bit范围: 16
llama-3.2-3b - Type01模式: 3
llama-3.2-3b - 测试无错误基准...

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:06<00:06,  6.97s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.08s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.51s/it]
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 370.87it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 375.93it/s]
100%|██████████| 100/100 [00:00<00:00, 376.24it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:49,  1.99it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:16,  5.80it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:08, 10.54it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:00<00:06, 13.92it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:05, 16.21it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:04, 17.94it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:01<00:04, 19.18it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:01<00:03, 20.04it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:01<00:03, 20.58it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:01<00:03, 21.00it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:01<00:03, 21.24it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:01<00:03, 21.45it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:02<00:02, 21.63it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:02<00:02, 21.77it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:02<00:02, 21.85it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:02<00:02, 21.91it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:02<00:02, 21.92it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:02<00:02, 21.69it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:02<00:02, 21.63it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:03<00:01, 21.66it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:03<00:01, 21.68it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:03<00:01, 21.68it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:03<00:01, 21.77it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:03<00:01, 21.77it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:03<00:01, 21.78it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:03<00:01, 21.70it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:04<00:01, 21.72it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:04<00:00, 21.75it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:04<00:00, 21.44it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:04<00:00, 21.71it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:04<00:00, 21.89it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:04<00:00, 21.95it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:04<00:00, 21.94it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:05<00:00, 21.91it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:05<00:00, 19.77it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
lambada_openai acc: 0.7000
llama-3.2-3b - 基准准确率: 0.7000
llama-3.2-3b - 测试 error_number: 100, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.76s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  2.83s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.12s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 100 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试 error_number: 500, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.55s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.70s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.98s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 500 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试 error_number: 1000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.41s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.61s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.88s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 1000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试 error_number: 3000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.21s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.58s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.82s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 3000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试 error_number: 5000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.78s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  2.80s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.09s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 5000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试 error_number: 8000, bit_range: 16, type01: 3

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.64s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.70s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.99s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 8000 测试出错: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 固定错误数目实验完成，结果保存到: results/llama-3.2-3b_fixed_errors_results_20250722_184547.json

llama-3.2-3b - 实验结果摘要:
错误数目	准确率	准确率下降
0		0.7000	0.0000
