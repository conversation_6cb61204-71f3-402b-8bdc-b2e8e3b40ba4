`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_<PERSON>ai from None to 0
开始加载模型...
错误数目列表: [100, 500, 1000, 3000, 5000]
Bit范围: 16
Type01模式: 3
测试无错误基准...
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 377.53it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 377.89it/s]
100%|██████████| 100/100 [00:00<00:00, 378.41it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:36,  2.72it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:09,  9.97it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:00<00:05, 16.83it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:00<00:04, 20.33it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:00<00:03, 22.98it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:00<00:03, 25.52it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:01<00:02, 27.13it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:01<00:02, 27.84it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:01<00:02, 28.65it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:01<00:02, 29.22it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:01<00:02, 29.54it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:01<00:01, 29.80it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:01<00:01, 30.02it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:01<00:01, 30.03it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:02<00:01, 29.80it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:02<00:01, 30.13it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:02<00:01, 30.12it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:02<00:01, 30.30it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:02<00:01, 30.39it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:02<00:00, 30.45it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:02<00:00, 30.43it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:03<00:00, 30.50it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:03<00:00, 30.57it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:03<00:00, 30.73it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:03<00:00, 30.87it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:03<00:00, 30.85it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:03<00:00, 27.51it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
