开始加载模型: mistral-7b
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 2

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.21s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.42s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:10<00:00,  3.35s/it]
Loading checkpoint shards: 100%|██████████| 3/3 [00:10<00:00,  3.35s/it]
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
`trust_remote_code` is not supported anymore.
Please check that the Hugging Face dataset 'EleutherAI/lambada_openai' isn't based on a loading script and remove `trust_remote_code`.
If the dataset is based on a loading script, please ask the dataset author to remove it and convert it to a standard format like Parquet.
Overwriting default num_fewshot of lambada_openai from None to 0
模型权重位数: torch.float16
Total bits flipped in weights: 0

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 375.23it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 375.63it/s]
100%|██████████| 100/100 [00:00<00:00, 376.73it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:18,  1.26it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:50,  1.92it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:42,  2.30it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:38,  2.47it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:35,  2.67it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.80it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:32,  2.86it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:31,  2.90it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:30,  2.97it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:29,  3.05it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:04<00:28,  3.11it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:27,  3.15it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:27,  3.18it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:26,  3.21it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:26,  3.22it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:27,  3.08it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:05<00:26,  3.13it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:26,  3.08it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:26,  3.06it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:06<00:26,  3.06it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:25,  3.06it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:25,  3.11it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:07<00:24,  3.15it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:23,  3.17it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:08<00:23,  3.19it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:08<00:23,  3.20it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:22,  3.20it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:09<00:22,  3.16it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:09<00:22,  3.10it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:22,  3.06it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:10<00:22,  3.05it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:10<00:21,  3.10it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:21,  3.14it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:11<00:20,  3.16it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:11<00:20,  3.19it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:19,  3.20it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:12<00:19,  3.21it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:12<00:19,  3.21it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:12<00:19,  3.21it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:13<00:18,  3.22it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:13<00:18,  3.15it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:13<00:18,  3.13it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:14<00:18,  3.11it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:14<00:17,  3.15it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:14<00:17,  3.18it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:15<00:16,  3.21it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:15<00:16,  3.22it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:15<00:16,  3.23it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:16<00:15,  3.25it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:16<00:15,  3.26it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:16<00:15,  3.25it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:17<00:14,  3.25it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:17<00:14,  3.17it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:17<00:14,  3.13it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:17<00:14,  3.11it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:18<00:13,  3.15it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:18<00:13,  3.19it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:18<00:13,  3.22it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:19<00:12,  3.24it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:19<00:12,  3.25it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:19<00:12,  3.25it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:20<00:11,  3.26it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:20<00:11,  3.26it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:20<00:11,  3.25it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:21<00:10,  3.19it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:21<00:10,  3.15it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:21<00:10,  3.12it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:22<00:10,  3.12it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:22<00:09,  3.16it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:22<00:09,  3.19it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:22<00:09,  3.20it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:23<00:08,  3.22it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:23<00:08,  3.24it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:23<00:07,  3.26it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:24<00:07,  3.26it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:24<00:07,  3.25it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:24<00:07,  3.21it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:25<00:06,  3.18it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:25<00:06,  3.13it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:25<00:06,  3.13it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:26<00:05,  3.17it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:26<00:05,  3.21it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:26<00:05,  3.23it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:27<00:04,  3.25it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:27<00:04,  3.25it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:27<00:04,  3.27it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:27<00:03,  3.27it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:28<00:03,  3.23it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:28<00:03,  3.18it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:28<00:03,  3.12it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:29<00:02,  3.08it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:29<00:02,  3.06it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:29<00:02,  3.13it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:30<00:01,  3.18it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:30<00:01,  3.21it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:30<00:01,  3.24it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:31<00:00,  3.26it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:31<00:00,  3.27it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:31<00:00,  3.30it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:31<00:00,  3.32it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:31<00:00,  3.13it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
lambada_openai acc: 0.7300
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 4

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.20s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.49s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.59s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=4: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 6

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.38s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.46s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.57s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=6: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 8

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.65s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.53s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.67s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=8: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 10

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.46s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.49s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.58s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=10: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 12

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.29s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.36s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.46s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=12: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 14

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.57s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.60s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.68s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=14: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-13, bit_range: 15

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.58s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.63s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.74s/it]
Error in mistral-7b type01=1, error_rate=1e-13, bit_range=15: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 2

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.20s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.37s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.47s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=2: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 4

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.33s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.55s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.64s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=4: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 6

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.62s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.53s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.67s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=6: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 8

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.73s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.72s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.82s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=8: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 10

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.58s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.52s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.64s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=10: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 12

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.54s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.60s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.72s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=12: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 14

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.75s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.56s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.72s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=14: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 5e-13, bit_range: 15

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.52s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.52s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.64s/it]
Error in mistral-7b type01=1, error_rate=5e-13, bit_range=15: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-12, bit_range: 2

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.52s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.59s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.71s/it]
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=2: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-12, bit_range: 4

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.29s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.18s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.32s/it]
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=4: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-12, bit_range: 6

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.11s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.33s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:06<00:03,  3.39s/it]
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=6: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-12, bit_range: 8

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:06,  3.32s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.57s/it]
Loading checkpoint shards:  67%|██████▋   | 2/3 [00:07<00:03,  3.62s/it]
Error in mistral-7b type01=1, error_rate=1e-12, bit_range=8: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 
mistral-7b - type01=1, error_rate: 1e-12, bit_range: 10

Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]
Loading checkpoint shards:  33%|███▎      | 1/3 [00:03<00:07,  3.50s/it]