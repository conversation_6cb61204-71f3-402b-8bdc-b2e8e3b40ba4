llama-3.2-3b - 开始实验

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.09s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.26s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:09<00:00,  4.68s/it]
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
模型权重位数: torch.float16

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 375.62it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 374.99it/s]
100%|██████████| 100/100 [00:00<00:00, 376.19it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:51,  1.91it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:16,  5.71it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:00<00:08, 10.62it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:00<00:06, 14.18it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:05, 16.72it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:01<00:04, 18.50it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:01<00:04, 19.84it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:01<00:03, 20.83it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:01<00:03, 21.54it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:01<00:03, 22.04it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:01<00:03, 22.42it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:01<00:02, 22.51it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:02<00:02, 22.66it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:02<00:02, 22.82it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:02<00:02, 22.95it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:02<00:02, 23.00it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:02<00:02, 23.13it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:02<00:02, 23.18it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:02<00:01, 23.21it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:02<00:01, 23.14it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:03<00:01, 23.17it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:03<00:01, 23.21it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:03<00:01, 23.21it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:03<00:01, 23.28it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:03<00:01, 23.26it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:03<00:01, 23.28it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:03<00:00, 23.33it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:03<00:00, 23.23it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:04<00:00, 22.92it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:04<00:00, 22.89it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:04<00:00, 23.13it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:04<00:00, 23.27it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:04<00:00, 23.38it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:04<00:00, 23.41it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:04<00:00, 20.77it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
lambada_openai acc: 0.7000
llama-3.2-3b - 基准准确率: 0.7000
llama-3.2-3b - 测试错误数目: 100

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.53s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  2.77s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:06<00:00,  3.04s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 100 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试错误数目: 500

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.47s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.65s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.92s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 500 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试错误数目: 1000

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.40s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.58s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.85s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 1000 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试错误数目: 3000

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.32s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.64s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.89s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 3000 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 测试错误数目: 5000

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:04<00:04,  4.35s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.57s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.84s/it]
模型权重位数: torch.float16
llama-3.2-3b - 错误数目 5000 测试失败: [enforce fail at alloc_cpu.cpp:117] err == 0. DefaultCPUAllocator: can't allocate memory: you tried to allocate 411231977472 bytes. Error code 12 (Cannot allocate memory)
llama-3.2-3b - 实验完成，结果保存到: results/llama-3.2-3b_fixed_errors_results_20250722_214538.json
