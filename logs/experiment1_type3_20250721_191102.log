`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_<PERSON>ai from None to 0
开始加载模型...
type01=3, error_rate: 1e-10, bit_range: 2
模型权重位数: torch.float16
Total bits flipped in weights: 81

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 374.80it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 377.78it/s]
100%|██████████| 100/100 [00:00<00:00, 375.60it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:46,  2.12it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:26,  3.75it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:20,  4.85it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:16,  5.76it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:00<00:14,  6.43it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:14,  6.70it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:13,  7.11it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:12,  7.41it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:11,  7.63it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:11,  7.78it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:01<00:11,  7.90it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:01<00:11,  7.96it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:01<00:10,  7.99it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:10,  7.98it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:02<00:10,  8.06it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:02<00:10,  8.09it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:02<00:10,  8.02it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:02<00:10,  8.04it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:02<00:10,  8.07it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:02<00:09,  8.12it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:02<00:09,  8.16it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:03<00:09,  8.19it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:03<00:09,  8.23it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:03<00:09,  8.26it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:03<00:09,  8.23it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:03<00:08,  8.26it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:03<00:08,  8.26it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:03<00:08,  8.23it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:03<00:08,  8.20it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:04<00:08,  8.23it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:04<00:08,  8.21it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:04<00:08,  8.24it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:04<00:08,  8.25it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:04<00:08,  8.25it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:04<00:07,  8.26it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:04<00:07,  8.28it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:04<00:07,  8.28it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:05<00:07,  8.31it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:05<00:07,  8.29it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:05<00:07,  8.29it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:05<00:07,  8.23it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:05<00:07,  8.21it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:05<00:06,  8.21it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:05<00:06,  8.21it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:05<00:06,  8.22it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:05<00:06,  8.25it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:06<00:06,  8.24it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:06<00:06,  8.23it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:06<00:06,  8.21it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:06<00:06,  8.21it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:06<00:05,  8.23it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:06<00:05,  8.21it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:06<00:05,  8.16it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:06<00:05,  8.14it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:07<00:05,  8.09it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:07<00:05,  8.06it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:07<00:05,  8.10it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:07<00:05,  8.13it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:07<00:05,  8.19it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:07<00:04,  8.21it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:07<00:04,  8.25it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:07<00:04,  8.12it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:08<00:04,  7.53it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:08<00:04,  7.73it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:08<00:04,  7.89it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:08<00:04,  8.03it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:08<00:04,  8.13it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:08<00:03,  8.16it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:08<00:03,  8.18it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:08<00:03,  8.21it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:09<00:03,  8.22it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:09<00:03,  8.25it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:09<00:03,  8.28it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:09<00:03,  8.26it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:09<00:03,  8.25it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:09<00:02,  8.02it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:09<00:02,  7.87it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:09<00:02,  7.56it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:10<00:02,  7.76it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:10<00:02,  7.89it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:10<00:02,  7.81it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:10<00:02,  7.93it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:10<00:02,  8.03it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:10<00:01,  8.11it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:10<00:01,  8.17it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:10<00:01,  8.21it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:11<00:01,  8.24it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:11<00:01,  8.24it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:11<00:01,  8.24it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:11<00:01,  8.22it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:11<00:01,  8.23it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:11<00:00,  8.19it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:11<00:00,  8.23it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:11<00:00,  8.25it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:12<00:00,  8.22it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:12<00:00,  8.01it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:12<00:00,  7.95it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:12<00:00,  8.01it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:12<00:00,  8.09it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  8.14it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:12<00:00,  7.90it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=3, error_rate: 1e-10, bit_range: 4
模型权重位数: torch.float16
Total bits flipped in weights: 165

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 376.80it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 373.81it/s]
100%|██████████| 100/100 [00:00<00:00, 374.51it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:20,  4.92it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:19,  4.95it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:00<00:19,  4.98it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:00<00:19,  5.01it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:18,  5.01it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:01<00:18,  5.03it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:01<00:18,  5.04it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:01<00:18,  5.00it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:01<00:18,  5.02it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:01<00:17,  5.04it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:02<00:17,  5.05it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:02<00:17,  5.05it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:02<00:17,  5.04it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:02<00:17,  5.04it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:02<00:16,  5.04it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:03<00:16,  5.05it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:03<00:16,  5.03it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:03<00:16,  5.04it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:03<00:16,  5.05it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:03<00:15,  5.06it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:04<00:15,  5.06it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:04<00:15,  5.07it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:04<00:15,  5.07it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:04<00:14,  5.08it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:04<00:14,  5.08it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:05<00:14,  5.08it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:05<00:14,  5.05it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:05<00:14,  5.01it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:05<00:14,  5.03it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:05<00:13,  5.05it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:06<00:13,  5.05it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:06<00:13,  5.06it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:06<00:13,  5.06it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:06<00:13,  5.08it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:06<00:12,  5.06it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:07<00:12,  5.06it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:07<00:12,  5.05it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:07<00:12,  5.04it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:07<00:12,  5.04it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:07<00:11,  5.04it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:08<00:11,  5.02it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:08<00:11,  5.02it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:08<00:11,  5.03it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:08<00:11,  5.04it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:08<00:10,  5.04it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:09<00:10,  5.06it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:09<00:10,  5.06it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:09<00:10,  5.02it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:09<00:10,  5.01it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:09<00:10,  5.00it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:10<00:09,  5.00it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:10<00:09,  4.97it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:10<00:09,  4.96it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:10<00:09,  4.96it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:10<00:09,  4.96it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:11<00:08,  4.94it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:11<00:08,  4.95it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:11<00:08,  4.95it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:11<00:08,  4.97it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:11<00:08,  4.97it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:12<00:07,  4.96it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:12<00:07,  4.98it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:12<00:07,  4.98it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:12<00:07,  4.99it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:12<00:07,  4.97it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:13<00:06,  4.94it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:13<00:06,  4.96it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:13<00:06,  4.96it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:13<00:06,  4.97it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:13<00:06,  4.97it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:14<00:05,  4.97it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:14<00:05,  4.95it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:14<00:05,  4.91it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:14<00:05,  4.89it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:14<00:05,  4.92it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:15<00:04,  4.90it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:15<00:04,  4.88it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:15<00:04,  4.89it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:15<00:04,  4.89it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:15<00:04,  4.89it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:16<00:03,  4.90it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:16<00:03,  4.91it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:16<00:03,  4.89it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:16<00:03,  4.88it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:17<00:03,  4.88it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:17<00:02,  4.90it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:17<00:02,  4.90it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:17<00:02,  4.90it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:17<00:02,  4.90it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:18<00:02,  4.90it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:18<00:01,  4.89it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:18<00:01,  4.87it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:18<00:01,  4.87it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:18<00:01,  4.87it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:19<00:01,  4.86it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:19<00:00,  4.87it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:19<00:00,  4.89it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:19<00:00,  4.90it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:19<00:00,  4.90it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.90it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:20<00:00,  4.98it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=3, error_rate: 1e-10, bit_range: 6
模型权重位数: torch.float16
Total bits flipped in weights: 219

  0%|          | 0/100 [00:00<?, ?it/s]
 35%|███▌      | 35/100 [00:00<00:00, 346.14it/s]
 71%|███████   | 71/100 [00:00<00:00, 353.72it/s]
100%|██████████| 100/100 [00:00<00:00, 356.24it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:34,  2.83it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:34,  2.85it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:34,  2.84it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:33,  2.85it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:33,  2.85it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:33,  2.84it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:32,  2.85it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:32,  2.84it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:31,  2.85it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:31,  2.85it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:31,  2.85it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:30,  2.86it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:30,  2.85it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:30,  2.85it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:29,  2.85it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:29,  2.86it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:05<00:28,  2.86it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:28,  2.86it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:28,  2.87it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:07<00:27,  2.86it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:27,  2.86it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.85it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:27,  2.84it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:26,  2.83it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:08<00:26,  2.83it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:26,  2.82it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:25,  2.82it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:09<00:25,  2.82it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:25,  2.82it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:24,  2.82it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:10<00:24,  2.82it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:24,  2.82it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:23,  2.82it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:11<00:23,  2.82it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:22,  2.84it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:21,  3.04it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:12<00:19,  3.19it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:18,  3.30it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:13<00:17,  3.39it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:13<00:17,  3.47it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:13<00:16,  3.51it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:14<00:16,  3.54it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:14<00:16,  3.54it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:14<00:15,  3.54it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:15<00:15,  3.56it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:15<00:15,  3.59it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:15<00:14,  3.60it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:15<00:14,  3.61it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:16<00:14,  3.62it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:16<00:13,  3.60it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:16<00:13,  3.58it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:17<00:13,  3.61it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:17<00:13,  3.57it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:17<00:12,  3.58it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:17<00:12,  3.62it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:18<00:12,  3.64it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:18<00:11,  3.64it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:18<00:11,  3.64it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:18<00:11,  3.64it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:19<00:10,  3.64it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:19<00:10,  3.65it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:19<00:10,  3.65it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:20<00:10,  3.61it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:20<00:09,  3.60it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:20<00:09,  3.60it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:20<00:09,  3.61it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:21<00:09,  3.61it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:21<00:08,  3.56it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:21<00:09,  3.23it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:22<00:09,  3.08it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:22<00:09,  2.95it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:22<00:09,  2.88it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:23<00:09,  2.86it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:23<00:09,  2.84it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:24<00:08,  2.81it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:24<00:08,  2.81it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:24<00:08,  2.80it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:25<00:07,  2.77it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:25<00:07,  2.75it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:25<00:07,  2.77it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:26<00:06,  2.78it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:26<00:06,  2.79it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:26<00:06,  2.76it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:27<00:05,  2.72it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:27<00:05,  2.72it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:28<00:05,  2.74it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:28<00:04,  2.76it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:28<00:04,  2.79it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:29<00:03,  2.78it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:29<00:03,  2.78it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:29<00:03,  2.80it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:30<00:02,  2.80it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:30<00:02,  2.79it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:30<00:02,  2.79it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:31<00:01,  2.81it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:31<00:01,  2.82it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:31<00:01,  2.83it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:32<00:00,  2.84it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:32<00:00,  2.85it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:32<00:00,  2.82it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:32<00:00,  3.03it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=3, error_rate: 1e-10, bit_range: 8
模型权重位数: torch.float16
Total bits flipped in weights: 302

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 372.42it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 374.25it/s]
100%|██████████| 100/100 [00:00<00:00, 371.43it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:34,  2.86it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:34,  2.86it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:34,  2.84it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:33,  2.84it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:01<00:33,  2.86it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:32,  2.86it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:32,  2.84it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:02<00:32,  2.86it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:31,  2.87it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:03<00:31,  2.86it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:03<00:31,  2.86it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:04<00:30,  2.85it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:04<00:30,  2.84it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:04<00:30,  2.84it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:05<00:29,  2.86it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:05<00:29,  2.86it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:05<00:29,  2.81it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:06<00:29,  2.83it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:06<00:28,  2.82it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:07<00:28,  2.80it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:07<00:28,  2.81it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:07<00:27,  2.83it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:08<00:27,  2.83it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:08<00:26,  2.83it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:08<00:26,  2.83it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:09<00:26,  2.81it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:09<00:25,  2.82it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:09<00:25,  2.81it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:10<00:25,  2.84it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:10<00:25,  2.80it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:10<00:24,  2.81it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:11<00:24,  2.83it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:11<00:23,  2.84it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:11<00:23,  2.85it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:12<00:22,  2.89it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:12<00:22,  2.90it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:13<00:21,  2.90it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:13<00:21,  2.89it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:13<00:20,  2.91it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:14<00:20,  2.90it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:14<00:20,  2.88it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:14<00:20,  2.89it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:15<00:19,  2.91it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:15<00:19,  2.91it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:15<00:18,  2.92it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:16<00:18,  2.91it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:16<00:18,  2.91it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:16<00:18,  2.89it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:17<00:17,  2.89it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:17<00:17,  2.89it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:17<00:16,  2.89it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:18<00:16,  2.90it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:18<00:16,  2.90it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:18<00:15,  2.91it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:19<00:15,  2.86it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:19<00:15,  2.86it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:19<00:15,  2.86it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:20<00:14,  2.88it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:20<00:14,  2.89it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:20<00:13,  2.91it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:21<00:13,  2.91it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:21<00:13,  2.89it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:21<00:12,  2.90it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:22<00:12,  2.89it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:22<00:12,  2.89it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:23<00:11,  2.90it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:23<00:11,  2.91it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:23<00:10,  2.92it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:24<00:10,  2.93it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:24<00:10,  2.89it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:24<00:10,  2.90it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:25<00:09,  2.90it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:25<00:09,  2.90it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:25<00:08,  2.91it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:26<00:08,  2.91it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:26<00:08,  2.91it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:26<00:07,  2.88it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:27<00:07,  2.89it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:27<00:07,  2.90it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:27<00:06,  2.91it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:28<00:06,  2.93it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:28<00:06,  2.92it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:28<00:05,  2.94it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:29<00:05,  2.92it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:29<00:05,  2.93it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:29<00:04,  2.93it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:30<00:04,  2.93it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:30<00:04,  2.94it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:30<00:03,  2.93it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:31<00:03,  2.94it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:31<00:03,  2.95it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:31<00:02,  2.91it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:32<00:02,  2.92it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:32<00:02,  2.93it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:32<00:01,  2.93it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:33<00:01,  2.94it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:33<00:01,  2.94it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:33<00:00,  2.95it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:34<00:00,  2.92it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:34<00:00,  2.93it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:34<00:00,  2.88it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=3, error_rate: 1e-10, bit_range: 10
模型权重位数: torch.float16
Total bits flipped in weights: 353

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.68it/s]
 75%|███████▌  | 75/100 [00:00<00:00, 374.24it/s]
100%|██████████| 100/100 [00:00<00:00, 346.69it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<00:41,  2.37it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:00<00:41,  2.38it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:41,  2.33it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:01<00:41,  2.30it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:02<00:40,  2.34it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:02<00:39,  2.36it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:02<00:39,  2.38it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:03<00:38,  2.40it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:03<00:37,  2.40it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:04<00:37,  2.42it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:04<00:36,  2.42it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:05<00:36,  2.41it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:05<00:37,  2.35it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:05<00:36,  2.37it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:06<00:35,  2.38it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:06<00:35,  2.38it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:07<00:34,  2.39it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:07<00:34,  2.40it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:07<00:33,  2.41it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:08<00:33,  2.42it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:08<00:32,  2.42it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:09<00:32,  2.43it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:09<00:31,  2.43it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:10<00:31,  2.43it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:10<00:30,  2.42it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:10<00:30,  2.42it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:11<00:30,  2.42it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:11<00:29,  2.41it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:12<00:29,  2.41it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:12<00:28,  2.42it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:12<00:28,  2.42it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:13<00:28,  2.41it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:13<00:27,  2.42it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:14<00:27,  2.41it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:14<00:26,  2.41it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:14<00:26,  2.42it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:15<00:26,  2.42it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:15<00:25,  2.43it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:16<00:25,  2.42it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:16<00:24,  2.42it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:17<00:24,  2.42it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:17<00:24,  2.41it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:17<00:23,  2.41it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:18<00:23,  2.42it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:18<00:22,  2.43it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:19<00:22,  2.42it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:19<00:21,  2.43it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:19<00:21,  2.43it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:20<00:20,  2.44it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:20<00:20,  2.44it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:21<00:20,  2.44it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:21<00:20,  2.38it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:22<00:19,  2.38it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:22<00:19,  2.41it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:22<00:18,  2.43it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:23<00:18,  2.44it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:23<00:17,  2.44it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:24<00:17,  2.45it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:24<00:16,  2.45it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:24<00:16,  2.44it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:25<00:16,  2.43it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:25<00:15,  2.42it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:26<00:15,  2.41it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:26<00:14,  2.41it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:26<00:14,  2.42it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:27<00:14,  2.42it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:27<00:13,  2.43it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:28<00:13,  2.43it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:28<00:12,  2.44it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:29<00:12,  2.44it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:29<00:11,  2.44it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:29<00:11,  2.44it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:30<00:11,  2.44it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:30<00:10,  2.40it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:31<00:10,  2.37it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:31<00:10,  2.38it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:31<00:09,  2.40it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:32<00:09,  2.41it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:32<00:08,  2.42it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:33<00:08,  2.44it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:33<00:07,  2.42it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:33<00:07,  2.44it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:34<00:06,  2.44it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:34<00:06,  2.45it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:35<00:06,  2.45it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:35<00:05,  2.45it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:36<00:05,  2.42it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:36<00:04,  2.41it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:36<00:04,  2.40it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:37<00:04,  2.40it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:37<00:03,  2.41it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:38<00:03,  2.41it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:38<00:02,  2.38it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:38<00:02,  2.39it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:39<00:02,  2.39it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:39<00:01,  2.38it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:40<00:01,  2.38it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:40<00:00,  2.39it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:41<00:00,  2.39it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:41<00:00,  2.40it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:41<00:00,  2.41it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=3, error_rate: 1e-10, bit_range: 12
模型权重位数: torch.float16
Total bits flipped in weights: 443

  0%|          | 0/100 [00:00<?, ?it/s]
 37%|███▋      | 37/100 [00:00<00:00, 369.88it/s]
 74%|███████▍  | 74/100 [00:00<00:00, 330.42it/s]
100%|██████████| 100/100 [00:00<00:00, 337.96it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:07,  1.48it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:04,  1.52it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<01:03,  1.53it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<01:03,  1.52it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<01:01,  1.54it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<00:55,  1.68it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<00:51,  1.79it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:04<00:49,  1.87it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:47,  1.92it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:05<00:45,  1.96it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:44,  1.99it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:06<00:43,  2.01it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:07<00:43,  2.02it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:07<00:42,  2.04it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:08<00:41,  2.04it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:08<00:41,  2.04it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:09<00:40,  2.05it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:09<00:40,  2.05it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:10<00:39,  2.06it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:10<00:39,  2.04it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:11<00:39,  2.02it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:11<00:38,  2.03it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:12<00:38,  2.02it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:12<00:37,  2.02it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:13<00:36,  2.03it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:13<00:36,  2.03it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:14<00:35,  2.04it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:14<00:35,  2.04it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:15<00:34,  2.04it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:15<00:34,  2.05it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:15<00:33,  2.05it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:16<00:33,  2.05it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:16<00:32,  2.05it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:17<00:32,  2.06it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:17<00:31,  2.05it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:18<00:31,  2.04it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:18<00:30,  2.05it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:19<00:30,  2.05it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:19<00:29,  2.06it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:20<00:29,  2.06it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:20<00:28,  2.06it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:21<00:28,  2.06it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:21<00:27,  2.06it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:22<00:27,  2.06it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:22<00:26,  2.06it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:23<00:26,  2.06it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:23<00:25,  2.06it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:24<00:25,  2.07it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:24<00:24,  2.08it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:25<00:24,  2.08it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:25<00:23,  2.08it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:26<00:23,  2.07it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:26<00:22,  2.07it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:27<00:22,  2.08it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:27<00:21,  2.08it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:28<00:21,  2.08it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:28<00:20,  2.08it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:29<00:20,  2.08it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:29<00:19,  2.08it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:29<00:19,  2.08it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:30<00:18,  2.08it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:30<00:18,  2.08it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:31<00:17,  2.08it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:31<00:17,  2.08it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:32<00:16,  2.08it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:32<00:16,  2.08it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:33<00:16,  2.06it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:33<00:15,  2.06it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:34<00:15,  2.06it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:34<00:14,  2.05it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:35<00:14,  2.04it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:35<00:13,  2.01it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:36<00:13,  2.01it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:36<00:12,  2.01it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:37<00:12,  2.00it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:37<00:11,  2.00it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:38<00:11,  1.98it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:38<00:11,  1.98it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:39<00:10,  1.99it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:39<00:10,  1.99it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:40<00:09,  2.00it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:40<00:09,  1.99it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:41<00:08,  1.99it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:41<00:08,  1.97it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:42<00:07,  1.97it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:42<00:07,  1.94it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:43<00:06,  1.91it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:43<00:06,  1.94it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:44<00:05,  1.95it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:44<00:05,  1.94it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:45<00:04,  1.96it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:46<00:04,  1.96it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:46<00:03,  1.97it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:47<00:03,  1.98it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:47<00:02,  1.99it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:48<00:02,  1.98it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:48<00:01,  1.96it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:49<00:01,  1.97it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:49<00:00,  1.98it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  2.00it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:50<00:00,  2.00it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5700
type01=3, error_rate: 1e-10, bit_range: 14
模型权重位数: torch.float16
Total bits flipped in weights: 493

  0%|          | 0/100 [00:00<?, ?it/s]
 38%|███▊      | 38/100 [00:00<00:00, 374.02it/s]
 76%|███████▌  | 76/100 [00:00<00:00, 373.77it/s]
100%|██████████| 100/100 [00:00<00:00, 373.58it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:01,  1.62it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<00:59,  1.66it/s]
Running loglikelihood requests:   3%|▎         | 3/100 [00:01<00:57,  1.68it/s]
Running loglikelihood requests:   4%|▍         | 4/100 [00:02<00:57,  1.68it/s]
Running loglikelihood requests:   5%|▌         | 5/100 [00:03<00:57,  1.65it/s]
Running loglikelihood requests:   6%|▌         | 6/100 [00:03<01:01,  1.53it/s]
Running loglikelihood requests:   7%|▋         | 7/100 [00:04<01:01,  1.51it/s]
Running loglikelihood requests:   8%|▊         | 8/100 [00:05<00:58,  1.58it/s]
Running loglikelihood requests:   9%|▉         | 9/100 [00:05<00:56,  1.62it/s]
Running loglikelihood requests:  10%|█         | 10/100 [00:06<00:54,  1.65it/s]
Running loglikelihood requests:  11%|█         | 11/100 [00:06<00:53,  1.68it/s]
Running loglikelihood requests:  12%|█▏        | 12/100 [00:07<00:51,  1.69it/s]
Running loglikelihood requests:  13%|█▎        | 13/100 [00:07<00:50,  1.71it/s]
Running loglikelihood requests:  14%|█▍        | 14/100 [00:08<00:50,  1.71it/s]
Running loglikelihood requests:  15%|█▌        | 15/100 [00:09<00:50,  1.69it/s]
Running loglikelihood requests:  16%|█▌        | 16/100 [00:09<00:49,  1.70it/s]
Running loglikelihood requests:  17%|█▋        | 17/100 [00:10<00:48,  1.71it/s]
Running loglikelihood requests:  18%|█▊        | 18/100 [00:10<00:47,  1.71it/s]
Running loglikelihood requests:  19%|█▉        | 19/100 [00:11<00:47,  1.72it/s]
Running loglikelihood requests:  20%|██        | 20/100 [00:11<00:46,  1.73it/s]
Running loglikelihood requests:  21%|██        | 21/100 [00:12<00:45,  1.73it/s]
Running loglikelihood requests:  22%|██▏       | 22/100 [00:13<00:45,  1.73it/s]
Running loglikelihood requests:  23%|██▎       | 23/100 [00:13<00:44,  1.73it/s]
Running loglikelihood requests:  24%|██▍       | 24/100 [00:14<00:43,  1.74it/s]
Running loglikelihood requests:  25%|██▌       | 25/100 [00:14<00:43,  1.74it/s]
Running loglikelihood requests:  26%|██▌       | 26/100 [00:15<00:42,  1.74it/s]
Running loglikelihood requests:  27%|██▋       | 27/100 [00:16<00:42,  1.73it/s]
Running loglikelihood requests:  28%|██▊       | 28/100 [00:16<00:41,  1.74it/s]
Running loglikelihood requests:  29%|██▉       | 29/100 [00:17<00:40,  1.74it/s]
Running loglikelihood requests:  30%|███       | 30/100 [00:17<00:40,  1.75it/s]
Running loglikelihood requests:  31%|███       | 31/100 [00:18<00:39,  1.73it/s]
Running loglikelihood requests:  32%|███▏      | 32/100 [00:18<00:38,  1.74it/s]
Running loglikelihood requests:  33%|███▎      | 33/100 [00:19<00:38,  1.75it/s]
Running loglikelihood requests:  34%|███▍      | 34/100 [00:20<00:37,  1.74it/s]
Running loglikelihood requests:  35%|███▌      | 35/100 [00:20<00:36,  1.76it/s]
Running loglikelihood requests:  36%|███▌      | 36/100 [00:21<00:36,  1.76it/s]
Running loglikelihood requests:  37%|███▋      | 37/100 [00:21<00:35,  1.77it/s]
Running loglikelihood requests:  38%|███▊      | 38/100 [00:22<00:34,  1.78it/s]
Running loglikelihood requests:  39%|███▉      | 39/100 [00:22<00:34,  1.79it/s]
Running loglikelihood requests:  40%|████      | 40/100 [00:23<00:33,  1.78it/s]
Running loglikelihood requests:  41%|████      | 41/100 [00:23<00:33,  1.78it/s]
Running loglikelihood requests:  42%|████▏     | 42/100 [00:24<00:32,  1.78it/s]
Running loglikelihood requests:  43%|████▎     | 43/100 [00:25<00:31,  1.78it/s]
Running loglikelihood requests:  44%|████▍     | 44/100 [00:25<00:31,  1.79it/s]
Running loglikelihood requests:  45%|████▌     | 45/100 [00:26<00:31,  1.77it/s]
Running loglikelihood requests:  46%|████▌     | 46/100 [00:26<00:30,  1.78it/s]
Running loglikelihood requests:  47%|████▋     | 47/100 [00:27<00:29,  1.79it/s]
Running loglikelihood requests:  48%|████▊     | 48/100 [00:27<00:28,  1.80it/s]
Running loglikelihood requests:  49%|████▉     | 49/100 [00:28<00:28,  1.79it/s]
Running loglikelihood requests:  50%|█████     | 50/100 [00:28<00:27,  1.79it/s]
Running loglikelihood requests:  51%|█████     | 51/100 [00:29<00:27,  1.80it/s]
Running loglikelihood requests:  52%|█████▏    | 52/100 [00:30<00:26,  1.80it/s]
Running loglikelihood requests:  53%|█████▎    | 53/100 [00:30<00:26,  1.80it/s]
Running loglikelihood requests:  54%|█████▍    | 54/100 [00:31<00:25,  1.78it/s]
Running loglikelihood requests:  55%|█████▌    | 55/100 [00:31<00:25,  1.78it/s]
Running loglikelihood requests:  56%|█████▌    | 56/100 [00:32<00:24,  1.78it/s]
Running loglikelihood requests:  57%|█████▋    | 57/100 [00:32<00:24,  1.79it/s]
Running loglikelihood requests:  58%|█████▊    | 58/100 [00:33<00:23,  1.79it/s]
Running loglikelihood requests:  59%|█████▉    | 59/100 [00:34<00:22,  1.79it/s]
Running loglikelihood requests:  60%|██████    | 60/100 [00:34<00:22,  1.79it/s]
Running loglikelihood requests:  61%|██████    | 61/100 [00:35<00:21,  1.78it/s]
Running loglikelihood requests:  62%|██████▏   | 62/100 [00:35<00:21,  1.78it/s]
Running loglikelihood requests:  63%|██████▎   | 63/100 [00:36<00:20,  1.77it/s]
Running loglikelihood requests:  64%|██████▍   | 64/100 [00:36<00:20,  1.76it/s]
Running loglikelihood requests:  65%|██████▌   | 65/100 [00:37<00:19,  1.76it/s]
Running loglikelihood requests:  66%|██████▌   | 66/100 [00:37<00:19,  1.76it/s]
Running loglikelihood requests:  67%|██████▋   | 67/100 [00:38<00:18,  1.75it/s]
Running loglikelihood requests:  68%|██████▊   | 68/100 [00:39<00:18,  1.76it/s]
Running loglikelihood requests:  69%|██████▉   | 69/100 [00:39<00:17,  1.77it/s]
Running loglikelihood requests:  70%|███████   | 70/100 [00:40<00:16,  1.78it/s]
Running loglikelihood requests:  71%|███████   | 71/100 [00:40<00:16,  1.79it/s]
Running loglikelihood requests:  72%|███████▏  | 72/100 [00:41<00:15,  1.78it/s]
Running loglikelihood requests:  73%|███████▎  | 73/100 [00:41<00:15,  1.79it/s]
Running loglikelihood requests:  74%|███████▍  | 74/100 [00:42<00:14,  1.79it/s]
Running loglikelihood requests:  75%|███████▌  | 75/100 [00:43<00:13,  1.80it/s]
Running loglikelihood requests:  76%|███████▌  | 76/100 [00:43<00:13,  1.79it/s]
Running loglikelihood requests:  77%|███████▋  | 77/100 [00:44<00:13,  1.77it/s]
Running loglikelihood requests:  78%|███████▊  | 78/100 [00:44<00:12,  1.76it/s]
Running loglikelihood requests:  79%|███████▉  | 79/100 [00:45<00:11,  1.76it/s]
Running loglikelihood requests:  80%|████████  | 80/100 [00:45<00:11,  1.76it/s]
Running loglikelihood requests:  81%|████████  | 81/100 [00:46<00:10,  1.74it/s]
Running loglikelihood requests:  82%|████████▏ | 82/100 [00:47<00:10,  1.74it/s]
Running loglikelihood requests:  83%|████████▎ | 83/100 [00:47<00:09,  1.74it/s]
Running loglikelihood requests:  84%|████████▍ | 84/100 [00:48<00:09,  1.74it/s]
Running loglikelihood requests:  85%|████████▌ | 85/100 [00:48<00:08,  1.73it/s]
Running loglikelihood requests:  86%|████████▌ | 86/100 [00:49<00:08,  1.73it/s]
Running loglikelihood requests:  87%|████████▋ | 87/100 [00:49<00:07,  1.73it/s]
Running loglikelihood requests:  88%|████████▊ | 88/100 [00:50<00:06,  1.74it/s]
Running loglikelihood requests:  89%|████████▉ | 89/100 [00:51<00:06,  1.74it/s]
Running loglikelihood requests:  90%|█████████ | 90/100 [00:51<00:05,  1.73it/s]
Running loglikelihood requests:  91%|█████████ | 91/100 [00:52<00:05,  1.73it/s]
Running loglikelihood requests:  92%|█████████▏| 92/100 [00:52<00:04,  1.73it/s]
Running loglikelihood requests:  93%|█████████▎| 93/100 [00:53<00:04,  1.74it/s]
Running loglikelihood requests:  94%|█████████▍| 94/100 [00:53<00:03,  1.73it/s]
Running loglikelihood requests:  95%|█████████▌| 95/100 [00:54<00:02,  1.71it/s]
Running loglikelihood requests:  96%|█████████▌| 96/100 [00:55<00:02,  1.73it/s]
Running loglikelihood requests:  97%|█████████▋| 97/100 [00:55<00:01,  1.75it/s]
Running loglikelihood requests:  98%|█████████▊| 98/100 [00:56<00:01,  1.77it/s]
Running loglikelihood requests:  99%|█████████▉| 99/100 [00:56<00:00,  1.77it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:57<00:00,  1.78it/s]
Running loglikelihood requests: 100%|██████████| 100/100 [00:57<00:00,  1.74it/s]
fatal: not a git repository (or any parent up to mount point /root)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).
`pretrained` model kwarg is not of type `str`. Many other model arguments may be ignored. Please do not launch via accelerate or use `parallelize=True` if passing an existing model this way.
Passed an already-initialized model through `pretrained`, assuming single-process call to evaluate() or custom distributed integration
Overwriting default num_fewshot of lambada_openai from None to 0
lambada_openai acc: 0.5600
type01=3, error_rate: 1e-10, bit_range: 16
模型权重位数: torch.float16
Total bits flipped in weights: 556

  0%|          | 0/100 [00:00<?, ?it/s]
 30%|███       | 30/100 [00:00<00:00, 294.70it/s]
 62%|██████▏   | 62/100 [00:00<00:00, 303.34it/s]
100%|██████████| 100/100 [00:00<00:00, 337.34it/s]
100%|██████████| 100/100 [00:00<00:00, 326.98it/s]

Running loglikelihood requests:   0%|          | 0/100 [00:00<?, ?it/s]
Running loglikelihood requests:   1%|          | 1/100 [00:00<01:05,  1.52it/s]
Running loglikelihood requests:   2%|▏         | 2/100 [00:01<01:03,  1.54it/s]