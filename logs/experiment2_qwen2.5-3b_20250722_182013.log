开始加载模型进行分布分析: qwen2.5-3b

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:03<00:03,  3.78s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.80s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:05<00:00,  2.94s/it]
/root/lanyun-tmp/error/utils/distribute.py:23: UserWarning: Tight layout not applied. tight_layout cannot make Axes height small enough to accommodate all Axes decorations.
  plt.tight_layout()
/root/lanyun-tmp/error/utils/distribute.py:55: UserWarning: Tight layout not applied. tight_layout cannot make Axes height small enough to accommodate all Axes decorations.
  plt.tight_layout()
模型权重位数: torch.float16
分析权重分布...
权重分布图已保存: results/weight_distribution_qwen2.5-3b_20250722_182013.png
分析激活分布...
处理样本 1/5: The quick brown fox jumps over the lazy dog....
处理样本 2/5: Machine learning is transforming the world of arti...
处理样本 3/5: Natural language processing enables computers to u...
处理样本 4/5: Deep learning models require large amounts of data...
处理样本 5/5: The future of AI depends on advances in computatio...
qwen2.5-3b 分布分析完成
