#!/bin/bash

# 实验一：不同type01模式下的准确率对比 - 支持四个模型
# type01=3: bit_range=[2,4,6,8,10,12,14,16]
# type01=1: bit_range=[2,4,6,8,10,12,14,15]
# type01=2: bit_range=[2,4,6,8,10,12,14,15]

set -e

mkdir -p results logs
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 定义模型列表
declare -a MODEL_PATHS=(
    "/root/lanyun-tmp/models/mistral-7b"

)

declare -a MODEL_NAMES=(
    "mistral-7b"

)

echo "=========================================="
echo "实验一：不同type01模式下的准确率对比"
echo "将测试 ${#MODEL_PATHS[@]} 个模型"
echo "=========================================="

# 检查参数
TYPE01=${1:-"all"}  # 默认运行所有类型
MODEL_FILTER=${2:-"all"}  # 默认运行所有模型

run_type_experiment() {
    local type01=$1
    local bit_range_list=$2
    local experiment_name=$3

    echo "运行 $experiment_name 实验..."

    for i in "${!MODEL_PATHS[@]}"; do
        model_path="${MODEL_PATHS[$i]}"
        model_name="${MODEL_NAMES[$i]}"

        # 如果指定了特定模型，跳过其他模型
        if [ "$MODEL_FILTER" != "all" ] && [ "$MODEL_FILTER" != "$model_name" ]; then
            continue
        fi

        echo "处理模型: $model_name"

        python3 -c "
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight, error_inject_activation
import json

print('开始加载模型: $model_name')
model_dir = '$model_path'
model_name = '$model_name'

error_rate_list=[0.0000000000001,0.0000000000005,0.000000000001,0.000000000005,0.00000000001,0.00000000005,0.0000000001,0.0000000005,0.000000001,0.000000005,0.00000001,0.00000005,0.0000001,0.0000005,0.000001]
bit_range_list = $bit_range_list
type01 = $type01

results = []
save_path = f'results/{model_name}_type{type01}_results_${TIMESTAMP}.json'

for error_rate in error_rate_list:
    for bit_range in bit_range_list:
        print(f'$model_name - type01={type01}, error_rate: {error_rate}, bit_range: {bit_range}')
        try:
            model, tokenizer = load_llama_model_and_tokenizer(model_dir)
            model = error_inject_weight(model, error_rate, bit_range, type01)
            hooks = error_inject_activation(model, error_rate, bit_range, type01)
            accuracy = eval_model(model, tokenizer)
            result_entry = {'model_name': model_name, 'type01': type01, 'error_rate': error_rate, 'bit_range': bit_range, 'accuracy': accuracy}
            results.append(result_entry)

            # 清理hooks
            for hook in hooks:
                hook.remove()

            # 保存中间结果
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f'Error in $model_name type01={type01}, error_rate={error_rate}, bit_range={bit_range}: {e}')
            continue
        finally:
            import torch, gc
            if 'model' in locals() and model is not None:
                del model
            if 'tokenizer' in locals() and tokenizer is not None:
                del tokenizer
            if 'hooks' in locals() and hooks is not None:
                for hook in hooks:
                    hook.remove()
                del hooks
            gc.collect()
            torch.cuda.empty_cache()

print(f'$model_name $experiment_name 实验完成，结果保存到: {save_path}')
" 2>&1 | tee logs/experiment1_${model_name}_type${type01}_${TIMESTAMP}.log
    done
}

if [ "$TYPE01" = "all" ] || [ "$TYPE01" = "3" ]; then
    run_type_experiment 3 "[2, 4, 6, 8, 10, 12, 14, 16]" "Type01=3"
fi

if [ "$TYPE01" = "all" ] || [ "$TYPE01" = "1" ]; then
    run_type_experiment 1 "[2, 4, 6, 8, 10, 12, 14, 15]" "Type01=1"
fi

if [ "$TYPE01" = "all" ] || [ "$TYPE01" = "2" ]; then
    run_type_experiment 2 "[2, 4, 6, 8, 10, 12, 14, 15]" "Type01=2"
fi

echo "实验一完成！结果保存在 results/ 目录下"
echo ""
echo "使用方法："
echo "  ./experiment1.sh                    # 运行所有类型，所有模型"
echo "  ./experiment1.sh 1                 # 只运行 type01=1，所有模型"
echo "  ./experiment1.sh 2                 # 只运行 type01=2，所有模型"
echo "  ./experiment1.sh 3                 # 只运行 type01=3，所有模型"
echo "  ./experiment1.sh all llama-3.2-1b  # 运行所有类型，只测试 llama-3.2-1b"
echo "  ./experiment1.sh 1 mistral-7b      # 只运行 type01=1，只测试 mistral-7b"
echo ""
echo "支持的模型名称："
for model_name in "${MODEL_NAMES[@]}"; do
    echo "  - $model_name"
done

# 检查并清理可能的重复文件
echo ""
echo "检查重复文件..."
for model_name in "${MODEL_NAMES[@]}"; do
    for type_num in 1 2 3; do
        files=(results/${model_name}_type${type_num}_results_*.json)
        if [ ${#files[@]} -gt 1 ]; then
            echo "警告：发现模型 $model_name type$type_num 的多个结果文件："
            for file in "${files[@]}"; do
                if [ -f "$file" ]; then
                    echo "  - $file"
                fi
            done
            echo "建议保留最新的文件，删除旧文件"
        fi
    done
done


