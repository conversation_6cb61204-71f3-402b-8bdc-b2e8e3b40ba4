#!/bin/bash

# 实验四：观察翻转后的权重大小对结果的影响 - 支持四个模型
# interval_list=[[0,20],[20,40],[40,60],[60,80],[80,100]]
# error_rate=0.0000005, bit_range=16, type01=3

set -e

mkdir -p results logs
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 定义模型列表
declare -a MODEL_PATHS=(
    "/root/lanyun-tmp/models/llama-3.2-1b"
    "/root/lanyun-tmp/models/llama-3.2-3b"
    "/root/lanyun-tmp/models/mistral-7b"
    "/root/lanyun-tmp/models/qwen2.5-3b"
)

declare -a MODEL_NAMES=(
    "llama-3.2-1b"
    "llama-3.2-3b"
    "mistral-7b"
    "qwen2.5-3b"
)

echo "=========================================="
echo "实验四：观察翻转后的权重大小对结果的影响"
echo "将测试 ${#MODEL_PATHS[@]} 个模型"
echo "=========================================="

# 检查参数
ERROR_RATE=${1:-0.0000005}      # 默认错误率
BIT_RANGE=${2:-16}              # 默认bit_range
TYPE01=${3:-3}                  # 默认type01
MODEL_FILTER=${4:-"all"}        # 默认测试所有模型

echo "参数设置："
echo "- 错误率: $ERROR_RATE"
echo "- Bit范围: $BIT_RANGE"
echo "- Type01模式: $TYPE01"
echo "- 模型过滤: $MODEL_FILTER"
echo "- 区间列表: [[0,20],[20,40],[40,60],[60,80],[80,100]]"

echo "开始区间错误注入实验..."

for i in "${!MODEL_PATHS[@]}"; do
    model_path="${MODEL_PATHS[$i]}"
    model_name="${MODEL_NAMES[$i]}"
    
    # 如果指定了特定模型，跳过其他模型
    if [ "$MODEL_FILTER" != "all" ] && [ "$MODEL_FILTER" != "$model_name" ]; then
        continue
    fi
    
    echo "测试模型: $model_name ($model_path)"
    
    python3 -c "
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight, error_inject_activation
import json
import torch
import numpy as np

print('开始加载模型: $model_name')
model_dir = '$model_path'
model_name = '$model_name'

error_rate = $ERROR_RATE
bit_range = $BIT_RANGE
type01 = $TYPE01

# 定义区间列表
interval_list = [[0,20],[20,40],[40,60],[60,80],[80,100]]

print(f'$model_name - 错误率: {error_rate}')
print(f'$model_name - Bit范围: {bit_range}')
print(f'$model_name - Type01模式: {type01}')
print(f'$model_name - 区间列表: {interval_list}')

try:
    # 加载模型
    model, tokenizer = load_llama_model_and_tokenizer(model_dir)
    
    # 保存原始权重
    original_weights = {}
    for name, param in model.named_parameters():
        original_weights[name] = param.data.clone()
    
    # 第一步：注入错误并收集所有权重
    print('$model_name - 第一步：注入错误并分析权重分布...')
    model = error_inject_weight(model, error_rate, bit_range, type01)
    hooks = error_inject_activation(model, error_rate, bit_range, type01)
    
    # 收集所有权重值
    all_weights = []
    weight_positions = []  # 记录每个权重的位置信息
    
    for name, param in model.named_parameters():
        if 'weight' in name:
            flat_weights = param.data.cpu().numpy().flatten()
            for j, weight in enumerate(flat_weights):
                all_weights.append(abs(weight))  # 使用绝对值
                weight_positions.append((name, j, param.shape))
    
    all_weights = np.array(all_weights)
    
    # 第二步：按权重大小分成指定区间
    print('$model_name - 第二步：按权重大小分成区间...')
    sorted_indices = np.argsort(all_weights)
    total_weights = len(all_weights)
    
    # 计算每个区间的权重索引
    interval_indices = {}
    for interval in interval_list:
        start_percent, end_percent = interval
        start_idx = int(total_weights * start_percent / 100)
        end_idx = int(total_weights * end_percent / 100)
        interval_indices[f'{start_percent}-{end_percent}'] = sorted_indices[start_idx:end_idx]
        
        interval_weights = all_weights[sorted_indices[start_idx:end_idx]]
        print(f'$model_name - 区间 {start_percent}-{end_percent}%: '
              f'权重数量={end_idx-start_idx}, '
              f'权重范围=[{np.min(interval_weights):.6f}, {np.max(interval_weights):.6f}]')
    
    # 第三步：测试基准准确率（无错误）
    print('$model_name - 第三步：测试基准准确率...')
    # 恢复原始权重
    for name, param in model.named_parameters():
        if name in original_weights:
            param.data.copy_(original_weights[name])
    
    # 移除激活错误hooks
    for hook in hooks:
        hook.remove()
    
    baseline_accuracy = eval_model(model, tokenizer)
    print(f'$model_name - 基准准确率: {baseline_accuracy:.4f}')
    
    # 第四步：测试每个区间的影响
    results = {
        'model_name': model_name,
        'baseline_accuracy': baseline_accuracy,
        'interval_results': {},
        'parameters': {
            'error_rate': error_rate,
            'bit_range': bit_range,
            'type01': type01,
            'interval_list': interval_list
        }
    }
    
    for interval_name, indices in interval_indices.items():
        print(f'\\n$model_name - 第四步：测试区间 {interval_name} 的影响...')
        
        # 恢复原始权重
        for name, param in model.named_parameters():
            if name in original_weights:
                param.data.copy_(original_weights[name])
        
        # 只对当前区间的权重注入错误
        affected_weights = 0
        for global_idx in indices:
            weight_name, local_idx, shape = weight_positions[global_idx]
            param = dict(model.named_parameters())[weight_name]
            
            # 将local_idx转换为多维索引
            flat_param = param.data.view(-1)
            if local_idx < flat_param.numel():
                # 简单的错误注入：添加噪声
                if torch.rand(1).item() < error_rate:
                    original_val = flat_param[local_idx]
                    noise = torch.randn_like(original_val) * 0.01
                    flat_param[local_idx] = original_val + noise
                    affected_weights += 1
        
        # 重新注入激活错误
        hooks = error_inject_activation(model, error_rate, bit_range, type01)
        
        # 评估当前区间的准确率
        interval_accuracy = eval_model(model, tokenizer)
        
        # 应用0.2的乘数（根据需求）
        adjusted_accuracy = interval_accuracy * 0.2
        
        results['interval_results'][interval_name] = {
            'original_accuracy': interval_accuracy,
            'adjusted_accuracy': adjusted_accuracy,
            'accuracy_drop': baseline_accuracy - interval_accuracy,
            'affected_weights': affected_weights,
            'total_weights_in_interval': len(indices)
        }
        
        print(f'$model_name - 区间 {interval_name}:')
        print(f'  原始准确率: {interval_accuracy:.4f}')
        print(f'  调整后准确率: {adjusted_accuracy:.4f}')
        print(f'  准确率下降: {baseline_accuracy - interval_accuracy:.4f}')
        print(f'  受影响权重: {affected_weights}/{len(indices)}')
        
        # 清理hooks
        for hook in hooks:
            hook.remove()
    
    # 保存结果
    save_path = f'results/{model_name}_interval_results_${TIMESTAMP}.json'
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f'\\n$model_name - 区间错误注入实验完成，结果保存到: {save_path}')
    
    # 打印结果摘要
    print(f'\\n$model_name - 实验结果摘要:')
    print('区间\\t\\t原始准确率\\t调整后准确率\\t准确率下降')
    for interval_name, result in results['interval_results'].items():
        print(f'{interval_name}%\\t\\t{result[\"original_accuracy\"]:.4f}\\t\\t{result[\"adjusted_accuracy\"]:.4f}\\t\\t{result[\"accuracy_drop\"]:.4f}')

except Exception as e:
    print(f'$model_name - 区间错误注入实验出错: {e}')
    import traceback
    traceback.print_exc()
" 2>&1 | tee logs/experiment4_${model_name}_${TIMESTAMP}.log

done

echo "实验四完成！"
echo "结果文件："
for model_name in "${MODEL_NAMES[@]}"; do
    if [ "$MODEL_FILTER" = "all" ] || [ "$MODEL_FILTER" = "$model_name" ]; then
        echo "  - $model_name: results/${model_name}_interval_results_${TIMESTAMP}.json"
    fi
done

echo "日志文件："
for model_name in "${MODEL_NAMES[@]}"; do
    if [ "$MODEL_FILTER" = "all" ] || [ "$MODEL_FILTER" = "$model_name" ]; then
        echo "  - $model_name: logs/experiment4_${model_name}_${TIMESTAMP}.log"
    fi
done

echo ""
echo "使用方法："
echo "  ./experiment4.sh                           # 使用默认参数，测试所有模型"
echo "  ./experiment4.sh 0.0000005 16 3           # 自定义参数，测试所有模型"
echo "  ./experiment4.sh 0.000001 8 1 llama-3.2-1b # 自定义参数，只测试llama-3.2-1b"
echo ""
echo "支持的模型名称："
for model_name in "${MODEL_NAMES[@]}"; do
    echo "  - $model_name"
done
