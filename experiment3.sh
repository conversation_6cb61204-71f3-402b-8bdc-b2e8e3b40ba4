#!/bin/bash

# 实验三：注入错误数目固定的情况下，模型的准确率 - 支持四个模型
# error_number_list=[100，500，1000，3000，5000，8000]，bit_range=16，type01=3

set -e

mkdir -p results logs
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 定义模型列表
declare -a MODEL_PATHS=(
    "/root/lanyun-tmp/models/llama-3.2-1b"
    "/root/lanyun-tmp/models/llama-3.2-3b"
    "/root/lanyun-tmp/models/mistral-7b"
    "/root/lanyun-tmp/models/qwen2.5-3b"
)

declare -a MODEL_NAMES=(
    "llama-3.2-1b"
    "llama-3.2-3b"
    "mistral-7b"
    "qwen2.5-3b"
)

# 检查参数
ERROR_NUMBERS=${1:-"100,500,1000,3000,5000"}  # 减少错误数目避免内存问题
BIT_RANGE=${2:-16}
TYPE01=${3:-3}
MODEL_FILTER=${4:-"all"}

echo "实验三：固定错误数目下的准确率测试"
echo "参数设置："
echo "- 错误数目列表: $ERROR_NUMBERS"
echo "- Bit范围: $BIT_RANGE"
echo "- Type01模式: $TYPE01"
echo "- 模型过滤: $MODEL_FILTER"

for i in "${!MODEL_PATHS[@]}"; do
    model_path="${MODEL_PATHS[$i]}"
    model_name="${MODEL_NAMES[$i]}"

    if [ "$MODEL_FILTER" != "all" ] && [ "$MODEL_FILTER" != "$model_name" ]; then
        continue
    fi

    echo "测试模型: $model_name"

    python3 -c "
import torch
import gc
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight_number
import json

model_dir = '$model_path'
model_name = '$model_name'
error_numbers_str = '$ERROR_NUMBERS'
error_number_list = [int(x.strip()) for x in error_numbers_str.split(',')]
bit_range = $BIT_RANGE
type01 = $TYPE01

results = []
save_path = f'results/{model_name}_fixed_errors_results_${TIMESTAMP}.json'

print(f'{model_name} - 开始实验')

# 测试基准
try:
    model, tokenizer = load_llama_model_and_tokenizer(model_dir)
    baseline_accuracy = eval_model(model, tokenizer)
    results.append({'model_name': model_name, 'error_number': 0, 'accuracy': baseline_accuracy})
    print(f'{model_name} - 基准准确率: {baseline_accuracy:.4f}')
    
    # 清理内存
    del model, tokenizer
    torch.cuda.empty_cache()
    gc.collect()
    
except Exception as e:
    print(f'{model_name} - 基准测试失败: {e}')

# 测试不同错误数目
for error_number in error_number_list:
    try:
        print(f'{model_name} - 测试错误数目: {error_number}')
        model, tokenizer = load_llama_model_and_tokenizer(model_dir)
        model = error_inject_weight_number(model, error_number, bit_range, type01)
        accuracy = eval_model(model, tokenizer)
        
        results.append({
            'model_name': model_name,
            'error_number': error_number,
            'accuracy': accuracy
        })
        
        print(f'{model_name} - 错误数目 {error_number}: 准确率 {accuracy:.4f}')
        
        # 保存中间结果
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 清理内存
        del model, tokenizer
        torch.cuda.empty_cache()
        gc.collect()
        
    except Exception as e:
        print(f'{model_name} - 错误数目 {error_number} 测试失败: {e}')
        continue

print(f'{model_name} - 实验完成，结果保存到: {save_path}')
" 2>&1 | tee logs/experiment3_${model_name}_${TIMESTAMP}.log

done

echo "实验三完成！"
echo "结果文件："
for model_name in "${MODEL_NAMES[@]}"; do
    if [ "$MODEL_FILTER" = "all" ] || [ "$MODEL_FILTER" = "$model_name" ]; then
        echo "  - $model_name: results/${model_name}_fixed_errors_results_${TIMESTAMP}.json"
    fi
done

echo "日志文件："
for model_name in "${MODEL_NAMES[@]}"; do
    if [ "$MODEL_FILTER" = "all" ] || [ "$MODEL_FILTER" = "$model_name" ]; then
        echo "  - $model_name: logs/experiment3_${model_name}_${TIMESTAMP}.log"
    fi
done

echo ""
echo "使用方法："
echo "  ./experiment3.sh                           # 使用默认参数，测试所有模型"
echo "  ./experiment3.sh \"100,500,1000\" 16 3      # 自定义错误数目列表、bit_range和type01，测试所有模型"
echo "  ./experiment3.sh \"50,100,200\" 8 1 llama-3.2-1b  # 测试较小的错误数目和不同参数，只测试llama-3.2-1b"
echo ""
echo "支持的模型名称："
for model_name in "${MODEL_NAMES[@]}"; do
    echo "  - $model_name"
done
python3 -c "
from utils.load_model import load_llama_model_and_tokenizer
from utils.eval_model import eval_model
from utils.error_inject import error_inject_weight_number, error_inject_activation
import json

print('开始加载模型...')
model_dir = '/root/lanyun-tmp/models/llama-3.2-1b'
model_name = 'llama-3.2-1b'

# 解析错误数目列表
error_numbers_str = '$ERROR_NUMBERS'
error_number_list = [int(x.strip()) for x in error_numbers_str.split(',')]
bit_range = $BIT_RANGE
type01 = $TYPE01

print(f'错误数目列表: {error_number_list}')
print(f'Bit范围: {bit_range}')
print(f'Type01模式: {type01}')

results = []
save_path = f'results/{model_name}_fixed_errors_results_${TIMESTAMP}.json'

# 首先测试无错误情况作为基准
print('测试无错误基准...')
try:
    model, tokenizer = load_llama_model_and_tokenizer(model_dir)
    baseline_accuracy = eval_model(model, tokenizer)
    baseline_entry = {'error_number': 0, 'bit_range': bit_range, 'type01': type01, 'accuracy': baseline_accuracy}
    results.append(baseline_entry)
    print(f'基准准确率: {baseline_accuracy:.4f}')
except Exception as e:
    print(f'基准测试出错: {e}')

# 测试不同错误数目
for error_number in error_number_list:
    print(f'测试 error_number: {error_number}, bit_range: {bit_range}, type01: {type01}')
    try:
        model, tokenizer = load_llama_model_and_tokenizer(model_dir)
        model = error_inject_weight_number(model, error_number, bit_range, type01)
        
        # 对于固定错误数目实验，主要关注权重错误
        # 如果需要同时测试激活错误，可以取消下面的注释
        # hooks = error_inject_activation(model, 0.0001, bit_range, type01)
        
        accuracy = eval_model(model, tokenizer)
        result_entry = {'error_number': error_number, 'bit_range': bit_range, 'type01': type01, 'accuracy': accuracy}
        results.append(result_entry)
        
        print(f'错误数目 {error_number}: 准确率 {accuracy:.4f}')
        
        # 保存中间结果
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        print(f'错误数目 {error_number} 测试出错: {e}')
        continue

print(f'固定错误数目实验完成，结果保存到: {save_path}')

# 打印结果摘要
print('\\n实验结果摘要:')
print('错误数目\\t准确率\\t准确率下降')
baseline_acc = results[0]['accuracy'] if results else 0
for result in results:
    error_num = result['error_number']
    accuracy = result['accuracy']
    drop = baseline_acc - accuracy if baseline_acc > 0 else 0
    print(f'{error_num}\\t\\t{accuracy:.4f}\\t{drop:.4f}')
" 2>&1 | tee logs/experiment3_${TIMESTAMP}.log

echo "实验三完成！"
echo "结果文件: results/llama-3.2-1b_fixed_errors_results_${TIMESTAMP}.json"
echo "日志文件: logs/experiment3_${TIMESTAMP}.log"
echo ""
echo "使用方法："
echo "  ./experiment3.sh                           # 使用默认参数"
echo "  ./experiment3.sh \"100,500,1000\" 16 3      # 自定义错误数目列表、bit_range和type01"
echo "  ./experiment3.sh \"50,100,200\" 8 1        # 测试较小的错误数目和不同参数"
