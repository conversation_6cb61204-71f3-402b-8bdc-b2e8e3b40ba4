
from typing import Dict, List, Callable, Any

class ActivationHook:
    """
    激活值钩子类，用于在模型推理过程中获取每个激活值
    """
    def __init__(self):
        self.activations = {}
        self.hooks = []

    def register_hook(self, module, name):
        """
        为指定模块注册钩子函数
        
        Args:
            module: PyTorch模块
            name: 激活值的名称标识
        """
        hook = module.register_forward_hook(self._get_activation_hook(name))
        self.hooks.append(hook)
        return hook

    def _get_activation_hook(self, name):
        """
        创建一个钩子函数来捕获激活值
        
        Args:
            name: 激活值的名称标识
        
        Returns:
            钩子函数
        """
        def hook(module, input, output):
            self.activations[name] = output
        return hook

    def clear(self):
        """
        清除所有存储的激活值
        """
        self.activations = {}

    def remove_hooks(self):
        """
        移除所有注册的钩子
        """
        for hook in self.hooks:
            hook.remove()
        self.hooks = []

    def get_activation(self, name):
        """
        获取指定名称的激活值
        
        Args:
            name: 激活值的名称标识
            
        Returns:
            对应的激活值，如果不存在则返回None
        """
        return self.activations.get(name, None)
    
    def get_all_activations(self):
        """
        获取所有激活值
        
        Returns:
            包含所有激活值的字典
        """
        return self.activations

