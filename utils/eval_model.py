import lm_eval
from lm_eval.models.huggingface import HFLM
from lm_eval.evaluator import simple_evaluate
import os



def eval_model(model, tokenizer):
    # Set environment variable to avoid tokenizer parallelism warning
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    lm_eval_model = HFLM(pretrained=model,tokenizer=tokenizer,)
    results = simple_evaluate(
        model=lm_eval_model,
        tasks=["lambada_openai"],
        num_fewshot=0,
        batch_size='auto',
        device="cuda",
        limit=100,
        bootstrap_iters=0  # Disable bootstrap to avoid overflow error
    )

    acc = results["results"]["lambada_openai"]["acc,none"]  # type: ignore
    ppl = results["results"]["lambada_openai"]["perplexity,none"]  # type: ignore
    print(f"lambada_openai acc: {acc:.4f}")
    print(f"lambada_openai perplexity: {ppl:.4f}")
    
    return acc,ppl