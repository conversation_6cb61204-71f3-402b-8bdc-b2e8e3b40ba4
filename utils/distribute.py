import numpy as np
import matplotlib.pyplot as plt
import torch
from collections import defaultdict

def distribute_weight(model):
    weights = defaultdict(list)

    for name, param in model.named_parameters():
        if 'weight' in name:
            layer_type = name.split('.')[-2]
            weights[layer_type].append(param.data.cpu().numpy().flatten())
    
    plt.figure(figsize=(12, 8))
    for i, (layer_type, layer_weights) in enumerate(weights.items()):
        plt.subplot(len(weights), 1, i+1)
        all_weights = np.concatenate(layer_weights)
        plt.hist(all_weights, bins=50, alpha=0.7)
        plt.title(f'Weight Distribution - {layer_type}')
        plt.xlabel('Weight Value')
        plt.ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('weight_distribution.png')
    plt.close()
    
    return weights

def distribute_activation(model, cal_data):
    activations = {}
    hooks = []
    def hook_fn(name):
        def hook(module, input, output):
            activations[name] = output.detach().cpu().numpy()
        return hook

    for name, module in model.named_modules():
        if isinstance(module, (torch.nn.ReLU, torch.nn.Conv2d, torch.nn.Linear)):
            hooks.append(module.register_forward_hook(hook_fn(name)))
 
    with torch.no_grad():
        model(cal_data)
    
    plt.figure(figsize=(12, 8))
    for i, (name, activation) in enumerate(activations.items()):
        if i >= 10:
            break
        plt.subplot(min(len(activations), 10), 1, i+1)
        act_flat = activation.flatten()
        plt.hist(act_flat, bins=50, alpha=0.7)
        plt.title(f'Activation Distribution - {name}')
        plt.xlabel('Activation Value')
        plt.ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('activation_distribution.png')
    plt.close()
    
    for hook in hooks:
        hook.remove()
    return activations