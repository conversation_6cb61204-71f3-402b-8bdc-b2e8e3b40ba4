import random
import torch

def bit_flip_tensor(tensor, error_rate, bit_range, type01=3):
    result = tensor.clone()
    total_flipped_bits = 0
    if tensor.dtype in [torch.float16, torch.float32]:
        if tensor.dtype == torch.float16:
            int_tensor = tensor.view(torch.int16)
            bits = 16
        elif tensor.dtype == torch.float32:
            int_tensor = tensor.view(torch.int32)
            bits = 32
        mask = torch.zeros_like(int_tensor)
        for bit in range(min(bit_range, bits)):
            rand_mask = torch.rand_like(tensor, dtype=torch.double) < error_rate
            
            if type01 == 1:
                bit_set_mask = (int_tensor & (1 << bit)) != 0
                bit_mask = rand_mask & bit_set_mask
            elif type01 == 2:
                bit_not_set_mask = (int_tensor & (1 << bit)) == 0
                bit_mask = rand_mask & bit_not_set_mask
            else:
                bit_mask = rand_mask
            
            total_flipped_bits += bit_mask.sum().item()
            mask = mask | (bit_mask.to(int_tensor.dtype) << bit)

        flipped_int = int_tensor ^ mask
        if tensor.dtype == torch.float16:
            result = flipped_int.view(torch.float16)
        elif tensor.dtype == torch.float32:
            result = flipped_int.view(torch.float32)
    else:
        total_mask = torch.zeros_like(tensor)
        for bit in range(min(bit_range, tensor.element_size() * 8)):
            rand_mask_bool = torch.rand_like(tensor, dtype=torch.float) < error_rate
            
            if type01 == 1:
                bit_set_mask = ((tensor & (1 << bit)) != 0)
                final_bit_mask = rand_mask_bool & bit_set_mask
            elif type01 == 2:
                bit_not_set_mask = ((tensor & (1 << bit)) == 0)
                final_bit_mask = rand_mask_bool & bit_not_set_mask
            else:
                final_bit_mask = rand_mask_bool

            total_flipped_bits += final_bit_mask.sum().item()
            bit_mask = final_bit_mask.to(tensor.dtype) << bit
            total_mask = total_mask | bit_mask
    
        result = result ^ total_mask
        
    return result, total_flipped_bits

def process_activation_hook(name, error_rate, bit_range, type01=3):
    def hook(module, input, output):
        if isinstance(output, tuple) and len(output) > 0 and isinstance(output[0], torch.Tensor):
            flipped_tensor, _ = bit_flip_tensor(output[0], error_rate, bit_range, type01)
            return (flipped_tensor,) + output[1:]
        elif isinstance(output, torch.Tensor):
            flipped_tensor, _ = bit_flip_tensor(output, error_rate, bit_range, type01)
            return flipped_tensor
        else:
            return output
    return hook

def error_inject_weight(model, error_rate, bit_range, type01=3):
    total_flipped_bits = 0
    for name, param in model.named_parameters():
        param.data, flipped_bits = bit_flip_tensor(param.data, error_rate, bit_range, type01)
        total_flipped_bits += flipped_bits
    print(f"Total bits flipped in weights: {total_flipped_bits}")
    return model

def error_inject_activation(model, error_rate, bit_range, type01=3):
    hooks = []
    for name, module in model.named_modules():
        if hasattr(module, 'forward'):
            hooks.append(module.register_forward_hook(process_activation_hook(name, error_rate, bit_range, type01)))
    return hooks

def error_inject_weight_number(model, error_number, bit_range, type01=3):
    all_params = []
    for name, param in model.named_parameters():
        all_params.append(param.data)
    total_bits = sum(param.numel() * min(bit_range, param.element_size() * 8) 
                     for param in all_params 
                     if param.dtype in [torch.float16, torch.float32, torch.int8, torch.int16, torch.int32, torch.int64])
    
    error_number = min(error_number, total_bits)
    error_positions = torch.randperm(total_bits)[:error_number]
    bit_count = 0
    total_flipped_bits = 0
    for param in all_params:
        if param.dtype not in [torch.float16, torch.float32, torch.int8, torch.int16, torch.int32, torch.int64]:
            continue
        param_bits = param.numel() * min(bit_range, param.element_size() * 8)
        param_errors = [pos - bit_count for pos in error_positions if bit_count <= pos < bit_count + param_bits]
        if param_errors:
            element_indices = [pos // min(bit_range, param.element_size() * 8) for pos in param_errors]
            bit_offsets = [pos % min(bit_range, param.element_size() * 8) for pos in param_errors]
            mask = torch.zeros_like(param.data, dtype=torch.int64)
            flat_param = param.data.reshape(-1)
            flat_mask = mask.reshape(-1)
            for idx, bit in zip(element_indices, bit_offsets):
                if type01 == 1 and (flat_param[idx].view(torch.int64) & (1 << bit)) >> bit == 1:
                    flat_mask[idx] |= (1 << bit)
                    total_flipped_bits += 1
                elif type01 == 2 and (flat_param[idx].view(torch.int64) & (1 << bit)) >> bit == 0:
                    flat_mask[idx] |= (1 << bit)
                    total_flipped_bits += 1
                elif type01 == 3:
                    flat_mask[idx] |= (1 << bit)
                    total_flipped_bits += 1
            
            if param.dtype == torch.float16:
                param.data = (param.data.view(torch.int16) ^ mask).view(torch.float16)
            elif param.dtype == torch.float32:
                param.data = (param.data.view(torch.int32) ^ mask).view(torch.float32)
            else:
                param.data = param.data ^ mask
        bit_count += param_bits

    print(f"Total bits flipped in weights: {total_flipped_bits}")
    return model


    """
    在推理时为当前捕获到的激活注入n个错误，n为error_number/总激活数目

    Args:
        model: PyTorch模型
        error_number: 要注入的错误总数
        bit_range: 位范围
        type01: 错误类型 (1: 只翻转1->0, 2: 只翻转0->1, 3: 随机翻转)

    Returns:
        hooks: 注册的钩子列表，用于在推理时注入错误
    """
    # 全局变量存储激活信息和错误位置
    activation_info = {
        'total_bits': 0,
        'error_positions': None,
        'bit_count': 0,
        'total_flipped_bits': 0
    }

    def calculate_activation_bits_hook(name):
        """计算激活的总位数的钩子"""
        def hook(module, input, output):
            if isinstance(output, tuple) and len(output) > 0 and isinstance(output[0], torch.Tensor):
                tensor = output[0]
            elif isinstance(output, torch.Tensor):
                tensor = output
            else:
                return output

            if tensor.dtype in [torch.float16, torch.float32, torch.int8, torch.int16, torch.int32, torch.int64]:
                tensor_bits = tensor.numel() * min(bit_range, tensor.element_size() * 8)
                activation_info['total_bits'] += tensor_bits

            return output
        return hook

    def error_injection_hook(name):
        """实际进行错误注入的钩子"""
        def hook(module, input, output):
            if isinstance(output, tuple) and len(output) > 0 and isinstance(output[0], torch.Tensor):
                tensor = output[0]
                is_tuple = True
            elif isinstance(output, torch.Tensor):
                tensor = output
                is_tuple = False
            else:
                return output

            if tensor.dtype not in [torch.float16, torch.float32, torch.int8, torch.int16, torch.int32, torch.int64]:
                return output

            # 计算当前激活的位数
            tensor_bits = tensor.numel() * min(bit_range, tensor.element_size() * 8)

            # 找到属于当前激活的错误位置
            current_start = activation_info['bit_count']
            current_end = activation_info['bit_count'] + tensor_bits

            if activation_info['error_positions'] is not None:
                tensor_errors = [pos - current_start for pos in activation_info['error_positions']
                               if current_start <= pos < current_end]

                if tensor_errors:
                    # 计算元素索引和位偏移
                    bits_per_element = min(bit_range, tensor.element_size() * 8)
                    element_indices = [pos // bits_per_element for pos in tensor_errors]
                    bit_offsets = [pos % bits_per_element for pos in tensor_errors]

                    # 创建掩码
                    if tensor.dtype == torch.float16:
                        mask = torch.zeros_like(tensor, dtype=torch.int16)
                        int_tensor = tensor.view(torch.int16)
                    elif tensor.dtype == torch.float32:
                        mask = torch.zeros_like(tensor, dtype=torch.int32)
                        int_tensor = tensor.view(torch.int32)
                    else:
                        mask = torch.zeros_like(tensor, dtype=torch.int64)
                        int_tensor = tensor.view(torch.int64)

                    # 展平张量以便索引
                    flat_tensor = int_tensor.reshape(-1)
                    flat_mask = mask.reshape(-1)

                    # 应用错误注入
                    for idx, bit in zip(element_indices, bit_offsets):
                        if idx < flat_tensor.numel():  # 确保索引有效
                            if type01 == 1:  # 只翻转1->0
                                if (flat_tensor[idx] & (1 << bit)) != 0:
                                    flat_mask[idx] |= (1 << bit)
                                    activation_info['total_flipped_bits'] += 1
                            elif type01 == 2:  # 只翻转0->1
                                if (flat_tensor[idx] & (1 << bit)) == 0:
                                    flat_mask[idx] |= (1 << bit)
                                    activation_info['total_flipped_bits'] += 1
                            else:  # 随机翻转
                                flat_mask[idx] |= (1 << bit)
                                activation_info['total_flipped_bits'] += 1

                    # 应用掩码
                    flipped_int = int_tensor ^ mask

                    # 转换回原始数据类型
                    if tensor.dtype == torch.float16:
                        result_tensor = flipped_int.view(torch.float16)
                    elif tensor.dtype == torch.float32:
                        result_tensor = flipped_int.view(torch.float32)
                    else:
                        result_tensor = flipped_int

                    activation_info['bit_count'] += tensor_bits

                    if is_tuple:
                        return (result_tensor,) + output[1:]
                    else:
                        return result_tensor

            activation_info['bit_count'] += tensor_bits
            return output
        return hook

    # 第一步：计算总激活位数
    calc_hooks = []
    for name, module in model.named_modules():
        if hasattr(module, 'forward'):
            calc_hooks.append(module.register_forward_hook(calculate_activation_bits_hook(name)))

    # 需要先进行一次前向传播来计算总位数
    # 注意：这需要在实际使用时提供输入数据
    # 这里我们返回一个特殊的钩子函数，它会在第一次调用时初始化错误位置

    def initialize_and_inject_hook(name):
        """初始化错误位置并注入错误的钩子"""
        def hook(module, input, output):
            # 如果还没有初始化错误位置，先初始化
            if activation_info['error_positions'] is None:
                # 移除计算钩子
                for calc_hook in calc_hooks:
                    calc_hook.remove()

                # 限制错误数量不超过总位数
                actual_error_number = min(error_number, activation_info['total_bits'])

                # 随机选择错误位置
                if activation_info['total_bits'] > 0:
                    activation_info['error_positions'] = torch.randperm(activation_info['total_bits'])[:actual_error_number].tolist()
                else:
                    activation_info['error_positions'] = []

                # 重置位计数器
                activation_info['bit_count'] = 0

                print(f"Total activation bits: {activation_info['total_bits']}")
                print(f"Injecting {len(activation_info['error_positions'])} errors into activations")

            # 执行错误注入
            return error_injection_hook(name)(module, input, output)
        return hook

    # 注册错误注入钩子
    hooks = []
    for name, module in model.named_modules():
        if hasattr(module, 'forward'):
            hooks.append(module.register_forward_hook(initialize_and_inject_hook(name)))

    return hooks