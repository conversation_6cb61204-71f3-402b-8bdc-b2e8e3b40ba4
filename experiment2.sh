#!/bin/bash

# 实验二：在推理中所有激活和权重的分布 - 支持四个模型

set -e

mkdir -p results logs
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 定义模型列表
declare -a MODEL_PATHS=(
    "/root/lanyun-tmp/models/llama-3.2-1b"
    "/root/lanyun-tmp/models/llama-3.2-3b"
    "/root/lanyun-tmp/models/qwen2.5-3b"
)

declare -a MODEL_NAMES=(
    "llama-3.2-1b"
    "llama-3.2-3b"
    "qwen2.5-3b"
)

echo "=========================================="
echo "实验二：激活和权重分布分析"
echo "将分析 ${#MODEL_PATHS[@]} 个模型"
echo "=========================================="

# 检查参数
MODEL_FILTER=${1:-"all"}  # 默认分析所有模型

echo "开始分析权重和激活分布..."

for i in "${!MODEL_PATHS[@]}"; do
    model_path="${MODEL_PATHS[$i]}"
    model_name="${MODEL_NAMES[$i]}"

    # 如果指定了特定模型，跳过其他模型
    if [ "$MODEL_FILTER" != "all" ] && [ "$MODEL_FILTER" != "$model_name" ]; then
        continue
    fi

    echo "分析模型: $model_name ($model_path)"

    python3 -c "
from utils.load_model import load_llama_model_and_tokenizer
from utils.distribute import distribute_weight, distribute_activation
import torch
import os

print('开始加载模型进行分布分析: $model_name')
model_dir = '$model_path'
model_name = '$model_name'

try:
    model, tokenizer = load_llama_model_and_tokenizer(model_dir)

    # 分析权重分布
    print('分析权重分布...')
    weight_dist = distribute_weight(model)

    # 重命名权重分布图
    if os.path.exists('weight_distribution.png'):
        os.rename('weight_distribution.png', f'results/weight_distribution_{model_name}_${TIMESTAMP}.png')
        print(f'权重分布图已保存: results/weight_distribution_{model_name}_${TIMESTAMP}.png')

    # 准备一些示例数据用于激活分析
    print('分析激活分布...')
    sample_texts = [
        'The quick brown fox jumps over the lazy dog.',
        'Machine learning is transforming the world of artificial intelligence.',
        'Natural language processing enables computers to understand human language.',
        'Deep learning models require large amounts of data for training.',
        'The future of AI depends on advances in computational power and algorithms.'
    ]

    for j, sample_text in enumerate(sample_texts):
        print(f'处理样本 {j+1}/{len(sample_texts)}: {sample_text[:50]}...')
        inputs = tokenizer(sample_text, return_tensors='pt', max_length=512, truncation=True).to('cuda')

        # 为每个样本生成单独的激活分布图
        activation_dist = distribute_activation(model, inputs['input_ids'])

        # 重命名生成的图片文件
        if os.path.exists('activation_distribution.png'):
            os.rename('activation_distribution.png', f'results/activation_distribution_{model_name}_sample_{j+1}_${TIMESTAMP}.png')

    print(f'$model_name 分布分析完成')

except Exception as e:
    print(f'$model_name 分布分析出错: {e}')
" 2>&1 | tee logs/experiment2_${model_name}_${TIMESTAMP}.log

done

echo "实验二完成！"
echo "结果文件："
for model_name in "${MODEL_NAMES[@]}"; do
    if [ "$MODEL_FILTER" = "all" ] || [ "$MODEL_FILTER" = "$model_name" ]; then
        echo "模型 $model_name:"
        echo "  - 权重分布图: results/weight_distribution_${model_name}_${TIMESTAMP}.png"
        echo "  - 激活分布图: results/activation_distribution_${model_name}_sample_*_${TIMESTAMP}.png"
        echo "  - 日志文件: logs/experiment2_${model_name}_${TIMESTAMP}.log"
    fi
done

echo ""
echo "使用方法："
echo "  ./experiment2.sh              # 分析所有模型"
echo "  ./experiment2.sh llama-3.2-1b # 只分析 llama-3.2-1b"
echo "  ./experiment2.sh mistral-7b   # 只分析 mistral-7b"
echo ""
echo "支持的模型名称："
for model_name in "${MODEL_NAMES[@]}"; do
    echo "  - $model_name"
done
python3 -c "
from utils.load_model import load_llama_model_and_tokenizer
from utils.distribute import distribute_weight, distribute_activation
import torch
import os

print('开始加载模型进行分布分析...')
model_dir = '/root/lanyun-tmp/models/llama-3.2-1b'
model, tokenizer = load_llama_model_and_tokenizer(model_dir)

# 分析权重分布
print('分析权重分布...')
try:
    weight_dist = distribute_weight(model)
    print('权重分布分析完成')
except Exception as e:
    print(f'权重分布分析出错: {e}')

# 准备一些示例数据用于激活分析
print('分析激活分布...')
try:
    sample_texts = [
        'The quick brown fox jumps over the lazy dog.',
        'Hello world, this is a test sentence.',
        'Machine learning is transforming the world.',
        'Natural language processing enables computers to understand human language.',
        'Deep learning models require large amounts of data for training.'
    ]
    
    for i, sample_text in enumerate(sample_texts):
        print(f'处理样本 {i+1}/{len(sample_texts)}: {sample_text[:50]}...')
        inputs = tokenizer(sample_text, return_tensors='pt', max_length=512, truncation=True).to('cuda')
        
        # 为每个样本生成单独的激活分布图
        activation_dist = distribute_activation(model, inputs['input_ids'])
        
        # 重命名生成的图片文件
        if os.path.exists('activation_distribution.png'):
            os.rename('activation_distribution.png', f'results/activation_distribution_sample_{i+1}_${TIMESTAMP}.png')
            
    print('激活分布分析完成')
except Exception as e:
    print(f'激活分布分析出错: {e}')

# 重命名权重分布图
if os.path.exists('weight_distribution.png'):
    os.rename('weight_distribution.png', f'results/weight_distribution_${TIMESTAMP}.png')
    print('权重分布图已保存')

print('分布分析完成！')
print('生成的文件：')
print(f'- 权重分布图: results/weight_distribution_${TIMESTAMP}.png')
print(f'- 激活分布图: results/activation_distribution_sample_*_${TIMESTAMP}.png')
" 2>&1 | tee logs/experiment2_${TIMESTAMP}.log

echo "实验二完成！"
echo "结果文件："
echo "- 权重分布图: results/weight_distribution_${TIMESTAMP}.png"
echo "- 激活分布图: results/activation_distribution_sample_*_${TIMESTAMP}.png"
echo "- 日志文件: logs/experiment2_${TIMESTAMP}.log"
